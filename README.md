# Chrome拼音标注插件

一个功能完整的Chrome浏览器插件，为网页中的中文文字添加拼音标注，帮助用户学习中文发音。

## 🚀 项目特性

### 核心功能
- **网页全文拼音标注**：一键为页面所有中文汉字添加拼音
- **双模式支持**：逐字标注模式和逐词标注模式（基于分词）
- **一键开关**：通过插件图标快速启用/禁用拼音显示
- **多种显示样式**：上方注音、悬浮提示、括号拼音

### 增强功能
- **悬浮拼音提示**：鼠标悬停显示，减少页面视觉干扰
- **生僻字优先标注**：智能识别常用字，只为难字显示拼音
- **多音字智能识别**：基于上下文语境选择正确读音
- **拼音文本复制**：支持复制带拼音的文本内容

### 个性化设置
- **多种显示样式**：上方注音、悬浮提示、括号拼音
- **拼音格式选项**：带声调、数字声调、无声调
- **标注范围控制**：全文、选中文字、特定区域（标题/正文）
- **颜色主题**：多种颜色主题可选

## 📁 项目结构

```
chrome-plugin-pinzi/
├── docs/                          # 项目文档
│   ├── 需求规格说明书.md
│   ├── 技术架构设计文档.md
│   ├── 用户界面设计规范.md
│   ├── 开发里程碑和时间计划.md
│   └── 测试用例和质量标准.md
├── src/                           # 源代码
│   ├── background/                # 后台脚本
│   │   └── background.js
│   ├── content/                   # 内容脚本
│   ├── popup/                     # 弹窗界面
│   │   ├── popup.html
│   │   ├── popup.js
│   │   └── popup.css
│   ├── options/                   # 设置页面
│   │   ├── options.html
│   │   ├── options.js
│   │   └── options.css
│   ├── core/                      # 核心算法模块
│   ├── utils/                     # 工具函数
│   │   ├── constants.js
│   │   ├── storage.js
│   │   └── logger.js
│   └── manifest.json              # 扩展配置文件
├── assets/                        # 静态资源
│   ├── icons/                     # 图标文件
│   ├── images/                    # 图片资源
│   └── styles/                    # 样式文件
├── tests/                         # 测试文件
│   ├── unit/                      # 单元测试
│   ├── integration/               # 集成测试
│   └── e2e/                       # 端到端测试
├── dist/                          # 构建输出
├── build/                         # 构建脚本
├── package.json                   # 项目配置
├── vite.config.js                 # Vite配置
├── vitest.config.js               # Vitest配置
├── .npmrc                         # pnpm配置
└── README.md                      # 项目说明
```

## 🛠️ 技术栈

- **扩展框架**：Chrome Extension Manifest V3
- **编程语言**：JavaScript (ES2022+)
- **构建工具**：Vite 5
- **包管理**：pnpm
- **代码规范**：ESLint + Prettier
- **测试框架**：Vitest
- **拼音转换**：pinyin-pro
- **中文分词**：nodejieba

## 📋 开发计划

### 第一阶段 - MVP核心功能 (Week 3-4)
- [x] 项目初始化和文档创建
- [x] 基础架构搭建
- [ ] 拼音转换核心算法
- [ ] 中文分词功能
- [ ] 逐字/逐词标注模式
- [ ] 一键开关功能

### 第二阶段 - 增强功能 (Week 7-8)
- [ ] 悬浮拼音提示功能
- [ ] 生僻字识别算法
- [ ] 多音字上下文分析
- [ ] 拼音文本复制功能

### 第三阶段 - 个性化设置 (Week 11-12)
- [ ] Options设置页面
- [ ] 多种显示样式
- [ ] 拼音格式选项
- [ ] 标注范围控制

### 第四阶段 - 扩展功能 (Week 13-14)
- [ ] 学习模式界面
- [ ] 字词释义集成
- [ ] 导出功能
- [ ] TTS语音播放

## 🚀 快速开始

### 环境要求
- Node.js 18+
- pnpm 8+
- Chrome 88+

### 安装依赖
```bash
pnpm install
```

### 开发模式
```bash
pnpm dev
```

### 构建生产版本
```bash
pnpm build
```

### 运行测试
```bash
pnpm test
```

### 代码检查
```bash
pnpm lint
```

### 打包发布
```bash
pnpm package
```

## 📖 使用说明

1. **安装插件**：将构建后的dist目录加载到Chrome扩展程序中
2. **启用标注**：点击插件图标启用拼音标注功能
3. **选择模式**：在弹窗中选择逐字或逐词标注模式
4. **个性化设置**：点击"更多设置"进入详细配置页面
5. **使用功能**：在任意网页上享受拼音标注功能

## 🧪 测试

项目包含完整的测试套件：

- **单元测试**：测试独立的函数和模块
- **集成测试**：测试组件间的交互
- **端到端测试**：测试完整的用户场景

运行所有测试：
```bash
pnpm test
```

查看测试覆盖率：
```bash
pnpm test:coverage
```

## 📝 贡献指南

1. Fork 项目
2. 创建功能分支 (`git checkout -b feature/AmazingFeature`)
3. 提交更改 (`git commit -m 'Add some AmazingFeature'`)
4. 推送到分支 (`git push origin feature/AmazingFeature`)
5. 打开 Pull Request

## 📄 许可证

本项目采用 MIT 许可证 - 查看 [LICENSE](LICENSE) 文件了解详情

## 🤝 支持

如果您遇到任何问题或有功能建议，请：

1. 查看 [文档](docs/)
2. 搜索 [Issues](../../issues)
3. 创建新的 [Issue](../../issues/new)

## 📊 项目状态

- **当前版本**：1.0.0
- **开发状态**：开发中
- **测试覆盖率**：目标 80%+
- **支持浏览器**：Chrome 88+

## 🙏 致谢

感谢以下开源项目的支持：
- [pinyin-pro](https://github.com/zh-lx/pinyin-pro) - 拼音转换库
- [nodejieba](https://github.com/yanyiwu/nodejieba) - 中文分词库
- [Vite](https://vitejs.dev/) - 现代化构建工具
- [Vitest](https://vitest.dev/) - 快速单元测试框架
- [pnpm](https://pnpm.io/) - 高效的包管理器
- [Chrome Extensions API](https://developer.chrome.com/docs/extensions/) - Chrome扩展开发文档
