{"name": "chrome-pinyin-extension", "version": "1.0.0", "description": "Chrome浏览器拼音标注插件，为网页中的中文文字添加拼音标注", "type": "module", "main": "dist/manifest.json", "scripts": {"dev": "vite build --watch --mode development", "build": "vite build", "preview": "vite preview", "test": "vitest", "test:watch": "vitest --watch", "test:coverage": "vitest --coverage", "lint": "eslint src/**/*.js", "lint:fix": "eslint src/**/*.js --fix", "format": "prettier --write src/**/*.{js,css,html}", "clean": "<PERSON><PERSON><PERSON> dist", "package": "pnpm build && web-ext build --source-dir=dist", "start": "web-ext run --source-dir=dist"}, "keywords": ["chrome-extension", "pinyin", "chinese", "annotation", "language-learning", "education"], "author": "Your Name", "license": "MIT", "repository": {"type": "git", "url": "https://github.com/yourusername/chrome-pinyin-extension.git"}, "bugs": {"url": "https://github.com/yourusername/chrome-pinyin-extension/issues"}, "homepage": "https://github.com/yourusername/chrome-pinyin-extension#readme", "devDependencies": {"@vitejs/plugin-legacy": "^5.4.0", "eslint": "^9.0.0", "eslint-config-prettier": "^9.1.0", "eslint-plugin-prettier": "^5.1.0", "jsdom": "^24.0.0", "prettier": "^3.2.0", "rimraf": "^5.0.0", "vite": "^5.2.0", "vite-plugin-web-extension": "^4.1.0", "vitest": "^1.4.0", "web-ext": "^7.11.0"}, "dependencies": {"pinyin-pro": "^3.18.0", "nodejieba": "^2.6.0"}, "vitest": {"environment": "jsdom", "coverage": {"include": ["src/**/*.js"], "exclude": ["src/**/*.test.js", "src/vendor/**"], "thresholds": {"branches": 80, "functions": 80, "lines": 80, "statements": 80}}}, "prettier": {"semi": true, "trailingComma": "es5", "singleQuote": true, "printWidth": 80, "tabWidth": 2, "useTabs": false}, "browserslist": ["Chrome >= 88"]}