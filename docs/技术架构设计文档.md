# Chrome拼音标注插件 - 技术架构设计文档

## 1. 架构概述

### 1.1 整体架构
本项目采用Chrome Extension Manifest V3架构，包含以下核心组件：
- Service Worker（后台脚本）
- Content Scripts（内容脚本）
- Popup界面
- Options页面
- 核心算法模块

### 1.2 技术栈选择
- **扩展框架**：Chrome Extension Manifest V3
- **编程语言**：JavaScript (ES2022+)
- **UI框架**：原生HTML/CSS + 少量JavaScript
- **构建工具**：Vite 5
- **包管理**：pnpm
- **代码规范**：ESLint + Prettier

## 2. 系统架构

### 2.1 组件架构图
```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Popup UI      │    │  Options Page   │    │  Content Script │
│                 │    │                 │    │                 │
│ - 开关控制      │    │ - 样式设置      │    │ - DOM操作       │
│ - 模式切换      │    │ - 格式选项      │    │ - 拼音标注      │
│ - 状态显示      │    │ - 范围控制      │    │ - 事件监听      │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         └───────────────────────┼───────────────────────┘
                                 │
                    ┌─────────────────┐
                    │ Service Worker  │
                    │                 │
                    │ - 消息路由      │
                    │ - 状态管理      │
                    │ - 存储管理      │
                    │ - 权限控制      │
                    └─────────────────┘
                                 │
                    ┌─────────────────┐
                    │   Core Modules  │
                    │                 │
                    │ - 拼音转换      │
                    │ - 中文分词      │
                    │ - 多音字处理    │
                    │ - DOM处理       │
                    └─────────────────┘
```

### 2.2 数据流架构
```
用户操作 → Popup/Options → Service Worker → Content Script → DOM更新 → 用户界面
    ↑                                                                      ↓
    └──────────────────── 状态反馈 ←─────────────────────────────────────┘
```

## 3. 核心模块设计

### 3.1 拼音转换模块 (PinyinConverter)
```javascript
class PinyinConverter {
  constructor(options = {}) {
    this.toneType = options.toneType || 'symbol'; // symbol, number, none
    this.polyphone = options.polyphone || true;
  }
  
  // 单字转拼音
  convertChar(char, context = '') {
    // 实现单字拼音转换逻辑
  }
  
  // 词组转拼音
  convertWord(word) {
    // 实现词组拼音转换逻辑
  }
  
  // 多音字处理
  resolvePolyphone(char, context) {
    // 基于上下文的多音字识别
  }
}
```

### 3.2 中文分词模块 (ChineseSegmenter)
```javascript
class ChineseSegmenter {
  constructor() {
    this.dictionary = new Map(); // 词典数据
  }
  
  // 分词处理
  segment(text) {
    // 实现中文分词算法
    // 返回词组数组
  }
  
  // 加载词典
  loadDictionary() {
    // 加载分词词典
  }
}
```

### 3.3 DOM处理模块 (DOMProcessor)
```javascript
class DOMProcessor {
  constructor() {
    this.processedNodes = new WeakSet();
    this.observer = null;
  }
  
  // 处理文本节点
  processTextNodes(container = document.body) {
    // 遍历并处理文本节点
  }
  
  // 添加拼音标注
  addPinyinAnnotation(textNode, mode = 'ruby') {
    // 添加拼音标注到DOM
  }
  
  // 移除拼音标注
  removePinyinAnnotation(container = document.body) {
    // 清理所有拼音标注
  }
  
  // 监听DOM变化
  observeChanges() {
    // 使用MutationObserver监听动态内容
  }
}
```

### 3.4 样式管理模块 (StyleManager)
```javascript
class StyleManager {
  constructor() {
    this.styleSheet = null;
    this.currentTheme = 'default';
  }
  
  // 应用样式
  applyStyles(styleType) {
    // 应用不同的拼音显示样式
  }
  
  // 创建样式表
  createStyleSheet() {
    // 动态创建CSS样式表
  }
  
  // 清理样式
  removeStyles() {
    // 移除所有自定义样式
  }
}
```

## 4. 数据存储设计

### 4.1 存储结构
```javascript
// Chrome Storage API 数据结构
const storageSchema = {
  // 用户设置
  settings: {
    enabled: true,              // 插件是否启用
    mode: 'word',              // 标注模式：'char' | 'word'
    displayStyle: 'ruby',      // 显示样式：'ruby' | 'tooltip' | 'bracket'
    toneType: 'symbol',        // 声调类型：'symbol' | 'number' | 'none'
    scope: 'all',              // 标注范围：'all' | 'selection' | 'title' | 'content'
    rareWordOnly: false,       // 仅标注生僻字
    autoEnable: false,         // 自动启用
    excludeDomains: []         // 排除域名列表
  },
  
  // 缓存数据
  cache: {
    pinyinCache: {},           // 拼音转换缓存
    segmentCache: {},          // 分词结果缓存
    frequencyData: {}          // 字频数据
  },
  
  // 统计数据
  stats: {
    totalUsage: 0,             // 总使用次数
    lastUsed: null,            // 最后使用时间
    favoriteWords: []          // 收藏词汇
  }
};
```

### 4.2 存储管理
```javascript
class StorageManager {
  // 获取设置
  async getSettings() {
    const result = await chrome.storage.sync.get('settings');
    return result.settings || defaultSettings;
  }
  
  // 保存设置
  async saveSettings(settings) {
    await chrome.storage.sync.set({ settings });
  }
  
  // 缓存管理
  async getCache(key) {
    const result = await chrome.storage.local.get(`cache.${key}`);
    return result[`cache.${key}`];
  }
  
  async setCache(key, value) {
    await chrome.storage.local.set({ [`cache.${key}`]: value });
  }
}
```

## 5. 消息通信设计

### 5.1 消息类型定义
```javascript
const MessageTypes = {
  // Popup → Service Worker
  TOGGLE_EXTENSION: 'toggle_extension',
  GET_STATUS: 'get_status',
  CHANGE_MODE: 'change_mode',
  
  // Service Worker → Content Script
  ENABLE_PINYIN: 'enable_pinyin',
  DISABLE_PINYIN: 'disable_pinyin',
  UPDATE_SETTINGS: 'update_settings',
  
  // Content Script → Service Worker
  STATUS_UPDATE: 'status_update',
  ERROR_REPORT: 'error_report',
  
  // Options → Service Worker
  SAVE_SETTINGS: 'save_settings',
  RESET_SETTINGS: 'reset_settings'
};
```

### 5.2 消息处理器
```javascript
class MessageHandler {
  constructor() {
    this.handlers = new Map();
    this.setupHandlers();
  }
  
  setupHandlers() {
    // 注册各种消息处理器
    this.handlers.set(MessageTypes.TOGGLE_EXTENSION, this.handleToggle);
    this.handlers.set(MessageTypes.GET_STATUS, this.handleGetStatus);
    // ... 其他处理器
  }
  
  async handleMessage(message, sender, sendResponse) {
    const handler = this.handlers.get(message.type);
    if (handler) {
      return await handler(message.data, sender);
    }
  }
}
```

## 6. 性能优化策略

### 6.1 缓存策略
- **拼音转换缓存**：缓存常用字词的拼音转换结果
- **分词结果缓存**：缓存分词结果，避免重复计算
- **DOM节点缓存**：标记已处理的节点，避免重复处理

### 6.2 懒加载策略
- **词典数据**：按需加载分词词典和字频数据
- **样式资源**：根据用户设置动态加载样式文件
- **功能模块**：延迟加载非核心功能模块

### 6.3 批处理优化
- **DOM操作批处理**：批量处理DOM修改，减少重排重绘
- **文本处理批处理**：批量处理文本转换，提高效率

### 6.4 内存管理
- **WeakMap/WeakSet**：使用弱引用避免内存泄漏
- **定期清理**：定期清理过期缓存和无用数据
- **资源释放**：及时释放不再使用的资源

## 7. 错误处理和日志

### 7.1 错误分类
```javascript
const ErrorTypes = {
  CONVERSION_ERROR: 'conversion_error',    // 拼音转换错误
  DOM_ERROR: 'dom_error',                  // DOM操作错误
  STORAGE_ERROR: 'storage_error',          // 存储错误
  NETWORK_ERROR: 'network_error',          // 网络错误
  PERMISSION_ERROR: 'permission_error'     // 权限错误
};
```

### 7.2 错误处理策略
- **优雅降级**：核心功能失败时提供基础功能
- **用户友好**：向用户显示易懂的错误信息
- **自动恢复**：尝试自动恢复常见错误
- **错误上报**：记录错误信息用于改进

### 7.3 日志系统
```javascript
class Logger {
  constructor(level = 'info') {
    this.level = level;
    this.levels = ['debug', 'info', 'warn', 'error'];
  }
  
  log(level, message, data = {}) {
    if (this.shouldLog(level)) {
      console[level](`[PinyinExtension] ${message}`, data);
      this.saveLog(level, message, data);
    }
  }
  
  debug(message, data) { this.log('debug', message, data); }
  info(message, data) { this.log('info', message, data); }
  warn(message, data) { this.log('warn', message, data); }
  error(message, data) { this.log('error', message, data); }
}
```

## 8. 安全考虑

### 8.1 内容安全策略 (CSP)
- 严格的CSP设置，防止XSS攻击
- 禁用eval和内联脚本
- 限制外部资源加载

### 8.2 权限最小化
- 只请求必要的权限
- 动态权限请求
- 用户明确授权

### 8.3 数据安全
- 不收集用户隐私数据
- 本地存储加密敏感信息
- 安全的消息传递机制

## 9. 测试策略

### 9.1 单元测试
- 核心算法模块测试
- 工具函数测试
- 边界条件测试

### 9.2 集成测试
- 组件间通信测试
- 端到端功能测试
- 性能基准测试

### 9.3 兼容性测试
- 不同Chrome版本测试
- 各种网站兼容性测试
- 移动设备测试

## 10. 部署和发布

### 10.1 构建流程
```bash
# 开发环境
pnpm dev

# 生产构建
pnpm build

# 测试
pnpm test

# 打包发布
pnpm package
```

### 10.2 版本管理
- 语义化版本控制
- 自动化版本发布
- 更新日志维护

### 10.3 发布流程
1. 代码审查和测试
2. 版本打包和签名
3. Chrome Web Store提交
4. 用户反馈收集和处理
