# Chrome拼音标注插件 - 用户界面设计规范

## 1. 设计原则

### 1.1 核心设计理念
- **简洁性**：界面简洁明了，避免复杂操作
- **一致性**：保持与Chrome浏览器界面风格一致
- **可访问性**：支持键盘导航和屏幕阅读器
- **响应性**：快速响应用户操作，提供即时反馈

### 1.2 用户体验原则
- **最少点击**：核心功能一键触达
- **状态明确**：清晰显示当前状态和设置
- **错误友好**：提供清晰的错误提示和解决方案
- **学习成本低**：新用户能够快速上手

## 2. 视觉设计规范

### 2.1 色彩规范
```css
/* 主色调 */
--primary-color: #1a73e8;      /* Chrome蓝 */
--primary-hover: #1557b0;      /* 悬停状态 */
--primary-active: #0d47a1;     /* 激活状态 */

/* 辅助色 */
--secondary-color: #5f6368;    /* 灰色文字 */
--success-color: #137333;      /* 成功绿 */
--warning-color: #ea8600;      /* 警告橙 */
--error-color: #d93025;        /* 错误红 */

/* 背景色 */
--bg-primary: #ffffff;         /* 主背景 */
--bg-secondary: #f8f9fa;       /* 次背景 */
--bg-hover: #f1f3f4;          /* 悬停背景 */

/* 边框色 */
--border-color: #dadce0;       /* 默认边框 */
--border-focus: #1a73e8;       /* 焦点边框 */

/* 文字色 */
--text-primary: #202124;       /* 主文字 */
--text-secondary: #5f6368;     /* 次文字 */
--text-disabled: #9aa0a6;      /* 禁用文字 */
```

### 2.2 字体规范
```css
/* 字体族 */
font-family: 'Roboto', 'Noto Sans SC', sans-serif;

/* 字体大小 */
--font-size-xs: 11px;          /* 小号文字 */
--font-size-sm: 12px;          /* 标准小文字 */
--font-size-base: 14px;        /* 基础文字 */
--font-size-lg: 16px;          /* 大号文字 */
--font-size-xl: 18px;          /* 标题文字 */

/* 行高 */
--line-height-tight: 1.2;      /* 紧凑行高 */
--line-height-base: 1.4;       /* 基础行高 */
--line-height-loose: 1.6;      /* 宽松行高 */
```

### 2.3 间距规范
```css
/* 间距系统 (8px基准) */
--spacing-xs: 4px;             /* 极小间距 */
--spacing-sm: 8px;             /* 小间距 */
--spacing-md: 16px;            /* 中等间距 */
--spacing-lg: 24px;            /* 大间距 */
--spacing-xl: 32px;            /* 超大间距 */

/* 组件内边距 */
--padding-xs: 4px 8px;         /* 小按钮内边距 */
--padding-sm: 6px 12px;        /* 标准按钮内边距 */
--padding-md: 8px 16px;        /* 大按钮内边距 */
```

### 2.4 圆角和阴影
```css
/* 圆角 */
--border-radius-sm: 4px;       /* 小圆角 */
--border-radius-md: 8px;       /* 中等圆角 */
--border-radius-lg: 12px;      /* 大圆角 */

/* 阴影 */
--shadow-sm: 0 1px 3px rgba(0,0,0,0.1);           /* 小阴影 */
--shadow-md: 0 2px 8px rgba(0,0,0,0.15);          /* 中等阴影 */
--shadow-lg: 0 4px 16px rgba(0,0,0,0.2);          /* 大阴影 */
```

## 3. 组件设计规范

### 3.1 按钮组件
```css
/* 主要按钮 */
.btn-primary {
  background: var(--primary-color);
  color: white;
  border: none;
  border-radius: var(--border-radius-sm);
  padding: var(--padding-sm);
  font-size: var(--font-size-base);
  cursor: pointer;
  transition: all 0.2s ease;
}

.btn-primary:hover {
  background: var(--primary-hover);
}

/* 次要按钮 */
.btn-secondary {
  background: transparent;
  color: var(--primary-color);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-sm);
  padding: var(--padding-sm);
}

/* 图标按钮 */
.btn-icon {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  border: none;
  background: transparent;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
}
```

### 3.2 开关组件
```css
.toggle-switch {
  position: relative;
  width: 44px;
  height: 24px;
  background: var(--border-color);
  border-radius: 12px;
  cursor: pointer;
  transition: background 0.2s ease;
}

.toggle-switch.active {
  background: var(--primary-color);
}

.toggle-switch::after {
  content: '';
  position: absolute;
  width: 20px;
  height: 20px;
  background: white;
  border-radius: 50%;
  top: 2px;
  left: 2px;
  transition: transform 0.2s ease;
  box-shadow: var(--shadow-sm);
}

.toggle-switch.active::after {
  transform: translateX(20px);
}
```

### 3.3 下拉选择组件
```css
.select-dropdown {
  position: relative;
  min-width: 120px;
}

.select-trigger {
  width: 100%;
  padding: var(--padding-sm);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-sm);
  background: white;
  cursor: pointer;
  display: flex;
  justify-content: space-between;
  align-items: center;
}

.select-options {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  background: white;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-sm);
  box-shadow: var(--shadow-md);
  z-index: 1000;
  max-height: 200px;
  overflow-y: auto;
}

.select-option {
  padding: var(--padding-sm);
  cursor: pointer;
  transition: background 0.2s ease;
}

.select-option:hover {
  background: var(--bg-hover);
}
```

## 4. 界面布局设计

### 4.1 Popup界面布局
```
┌─────────────────────────────────┐
│  Chrome拼音标注插件              │  ← 标题栏
├─────────────────────────────────┤
│  ●○ 启用拼音标注                │  ← 主开关
├─────────────────────────────────┤
│  标注模式                       │
│  ○ 逐字标注  ● 逐词标注         │  ← 模式选择
├─────────────────────────────────┤
│  显示样式                       │
│  [上方注音 ▼]                   │  ← 样式选择
├─────────────────────────────────┤
│  [更多设置]  [帮助]             │  ← 操作按钮
└─────────────────────────────────┘
```

尺寸规范：
- 宽度：320px
- 高度：自适应（最小200px）
- 内边距：16px

### 4.2 Options页面布局
```
┌─────────────────────────────────────────────────────────┐
│  Chrome拼音标注插件 - 设置                               │
├─────────────────────────────────────────────────────────┤
│  ┌─ 基础设置 ─────────────────────────────────────────┐  │
│  │  □ 自动启用拼音标注                               │  │
│  │  □ 仅标注生僻字                                   │  │
│  │  标注范围：[全文 ▼]                               │  │
│  └─────────────────────────────────────────────────────┘  │
│                                                         │
│  ┌─ 显示设置 ─────────────────────────────────────────┐  │
│  │  显示样式：[上方注音 ▼]                           │  │
│  │  拼音格式：[带声调 ▼]                             │  │
│  │  字体大小：[中等 ▼]                               │  │
│  └─────────────────────────────────────────────────────┘  │
│                                                         │
│  ┌─ 高级设置 ─────────────────────────────────────────┐  │
│  │  排除网站：                                       │  │
│  │  [输入域名...]                    [添加]          │  │
│  │  • example.com                    [删除]          │  │
│  └─────────────────────────────────────────────────────┘  │
│                                                         │
│  [保存设置]  [重置默认]  [导入/导出]                    │
└─────────────────────────────────────────────────────────┘
```

### 4.3 拼音标注样式设计

#### 4.3.1 上方注音样式 (Ruby)
```html
<ruby>
  汉<rt>hàn</rt>
  字<rt>zì</rt>
</ruby>
```

```css
ruby {
  ruby-align: center;
}

rt {
  font-size: 0.7em;
  color: var(--primary-color);
  font-weight: normal;
  line-height: 1;
}
```

#### 4.3.2 悬浮提示样式
```css
.pinyin-tooltip {
  position: absolute;
  background: rgba(0, 0, 0, 0.8);
  color: white;
  padding: 4px 8px;
  border-radius: var(--border-radius-sm);
  font-size: var(--font-size-sm);
  z-index: 10000;
  pointer-events: none;
  white-space: nowrap;
}

.pinyin-tooltip::after {
  content: '';
  position: absolute;
  top: 100%;
  left: 50%;
  transform: translateX(-50%);
  border: 4px solid transparent;
  border-top-color: rgba(0, 0, 0, 0.8);
}
```

#### 4.3.3 括号拼音样式
```css
.pinyin-bracket {
  color: var(--primary-color);
  font-size: 0.8em;
  margin-left: 2px;
}
```

## 5. 交互设计规范

### 5.1 状态反馈
- **加载状态**：显示加载动画，避免用户等待焦虑
- **成功状态**：绿色图标或文字提示操作成功
- **错误状态**：红色图标或文字提示错误信息
- **禁用状态**：灰色显示，明确表示不可操作

### 5.2 动画效果
```css
/* 淡入淡出 */
.fade-in {
  animation: fadeIn 0.3s ease-in-out;
}

@keyframes fadeIn {
  from { opacity: 0; }
  to { opacity: 1; }
}

/* 滑动效果 */
.slide-down {
  animation: slideDown 0.2s ease-out;
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-10px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

/* 脉冲效果 */
.pulse {
  animation: pulse 1s infinite;
}

@keyframes pulse {
  0% { transform: scale(1); }
  50% { transform: scale(1.05); }
  100% { transform: scale(1); }
}
```

### 5.3 键盘导航
- **Tab键**：在可交互元素间切换
- **Enter/Space**：激活按钮或开关
- **Esc键**：关闭弹窗或下拉菜单
- **方向键**：在选项间导航

## 6. 响应式设计

### 6.1 断点设置
```css
/* 小屏幕适配 */
@media (max-width: 480px) {
  .popup-container {
    width: 280px;
    padding: 12px;
  }
  
  .btn {
    padding: 8px 12px;
    font-size: 13px;
  }
}

/* 高分辨率屏幕适配 */
@media (-webkit-min-device-pixel-ratio: 2) {
  .icon {
    background-size: contain;
  }
}
```

## 7. 可访问性设计

### 7.1 语义化HTML
```html
<!-- 使用语义化标签 -->
<main role="main">
  <section aria-labelledby="settings-title">
    <h2 id="settings-title">基础设置</h2>
    <!-- 设置内容 -->
  </section>
</main>

<!-- 表单标签关联 -->
<label for="mode-select">标注模式：</label>
<select id="mode-select" aria-describedby="mode-help">
  <option value="char">逐字标注</option>
  <option value="word">逐词标注</option>
</select>
<div id="mode-help" class="help-text">
  选择拼音标注的粒度
</div>
```

### 7.2 ARIA属性
```html
<!-- 开关状态 -->
<button 
  role="switch" 
  aria-checked="true"
  aria-labelledby="toggle-label">
  <span id="toggle-label">启用拼音标注</span>
</button>

<!-- 下拉菜单 -->
<div role="combobox" aria-expanded="false" aria-haspopup="listbox">
  <button aria-label="选择显示样式">上方注音</button>
  <ul role="listbox" hidden>
    <li role="option" aria-selected="true">上方注音</li>
    <li role="option">悬浮提示</li>
  </ul>
</div>
```

### 7.3 颜色对比度
- **正常文字**：对比度至少4.5:1
- **大号文字**：对比度至少3:1
- **图标和按钮**：对比度至少3:1

## 8. 图标设计规范

### 8.1 图标尺寸
- **16x16px**：小图标（菜单项）
- **24x24px**：标准图标（按钮）
- **32x32px**：大图标（主要操作）
- **48x48px**：应用图标

### 8.2 图标风格
- **线条粗细**：2px
- **圆角半径**：2px
- **颜色**：单色，使用CSS控制
- **风格**：简洁、现代、符合Material Design

### 8.3 状态图标
```css
/* 启用状态 */
.icon-enabled {
  color: var(--success-color);
}

/* 禁用状态 */
.icon-disabled {
  color: var(--text-disabled);
}

/* 错误状态 */
.icon-error {
  color: var(--error-color);
}

/* 加载状态 */
.icon-loading {
  animation: spin 1s linear infinite;
}

@keyframes spin {
  from { transform: rotate(0deg); }
  to { transform: rotate(360deg); }
}
```

## 9. 多语言支持

### 9.1 文本国际化
```javascript
// 支持的语言
const supportedLocales = ['zh-CN', 'zh-TW', 'en', 'ja', 'ko'];

// 文本资源
const messages = {
  'zh-CN': {
    'extension_name': 'Chrome拼音标注插件',
    'enable_pinyin': '启用拼音标注',
    'char_mode': '逐字标注',
    'word_mode': '逐词标注'
  },
  'en': {
    'extension_name': 'Chrome Pinyin Extension',
    'enable_pinyin': 'Enable Pinyin',
    'char_mode': 'Character Mode',
    'word_mode': 'Word Mode'
  }
};
```

### 9.2 RTL语言支持
```css
/* RTL语言适配 */
[dir="rtl"] .popup-container {
  text-align: right;
}

[dir="rtl"] .btn-group {
  flex-direction: row-reverse;
}
```

## 10. 设计交付规范

### 10.1 设计文件组织
```
design/
├── wireframes/          # 线框图
├── mockups/            # 视觉稿
├── prototypes/         # 交互原型
├── assets/             # 设计资源
│   ├── icons/          # 图标文件
│   ├── images/         # 图片资源
│   └── fonts/          # 字体文件
└── specs/              # 设计规范
```

### 10.2 标注规范
- **尺寸标注**：使用px单位
- **颜色标注**：提供HEX和RGB值
- **字体标注**：包含字体族、大小、行高、字重
- **间距标注**：内边距、外边距、元素间距

### 10.3 切图规范
- **格式**：PNG（透明背景）、SVG（矢量图标）
- **命名**：语义化命名，使用连字符分隔
- **尺寸**：提供1x、2x、3x三种分辨率
- **优化**：压缩文件大小，保证质量
