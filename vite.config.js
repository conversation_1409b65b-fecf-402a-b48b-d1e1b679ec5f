import { defineConfig } from 'vite';
import { resolve } from 'path';
import { copyFileSync, mkdirSync, existsSync, readdirSync } from 'fs';

export default defineConfig({
  plugins: [
    {
      name: 'copy-files',
      writeBundle() {

        // 确保dist目录存在
        if (!existsSync('dist')) {
          mkdirSync('dist', { recursive: true });
        }

        // 复制manifest.json
        copyFileSync('src/manifest.json', 'dist/manifest.json');

        // 复制HTML文件到根目录
        copyFileSync('src/popup/popup.html', 'dist/popup.html');
        copyFileSync('src/options/options.html', 'dist/options.html');

        // 复制CSS文件
        if (existsSync('src/content/content.css')) {
          copyFileSync('src/content/content.css', 'dist/content.css');
        }

        // 复制图标
        if (existsSync('assets/icons')) {
          if (!existsSync('dist/icons')) {
            mkdirSync('dist/icons', { recursive: true });
          }
          const files = readdirSync('assets/icons');
          files.forEach(file => {
            copyFileSync(`assets/icons/${file}`, `dist/icons/${file}`);
          });
        }
      }
    }
  ],

  build: {
    outDir: 'dist',
    emptyOutDir: true,
    rollupOptions: {
      input: {
        background: resolve(__dirname, 'src/background/background.js'),
        content: resolve(__dirname, 'src/content/content.js'),
        popup: resolve(__dirname, 'src/popup/popup.js'),
        options: resolve(__dirname, 'src/options/options.js'),
      },
      output: {
        entryFileNames: (chunkInfo) => {
          // 为JS文件使用简单的名称
          if (chunkInfo.name === 'background' || chunkInfo.name === 'content') {
            return '[name].js';
          }
          return 'assets/[name]-[hash].js';
        },
        chunkFileNames: 'assets/[name]-[hash].js',
        assetFileNames: (assetInfo) => {
          // CSS文件
          if (assetInfo.names?.[0]?.endsWith('.css')) {
            return '[name][extname]';
          }
          return 'assets/[name][extname]';
        },
      },
    },
    minify: 'terser',
    terserOptions: {
      compress: {
        drop_console: true,
        drop_debugger: true,
      },
    },
  },

  resolve: {
    alias: {
      '@': resolve(__dirname, 'src'),
      '@core': resolve(__dirname, 'src/core'),
      '@utils': resolve(__dirname, 'src/utils'),
      '@assets': resolve(__dirname, 'assets'),
    },
  },

  // 开发服务器配置（虽然扩展开发不需要，但保留以备将来使用）
  server: {
    port: 3000,
    open: false,
  },

  // 预览服务器配置
  preview: {
    port: 3001,
  },

  // 环境变量配置
  define: {
    __DEV__: JSON.stringify(process.env.NODE_ENV === 'development'),
    __VERSION__: JSON.stringify(process.env.npm_package_version || '1.0.0'),
  },

  // CSS配置
  css: {
    postcss: {
      plugins: [
        // 可以在这里添加PostCSS插件
      ],
    },
  },

  // 优化配置
  optimizeDeps: {
    include: [
      'pinyin-pro',
      'nodejieba'
    ],
  },

  // 构建目标
  target: ['chrome88'],

  // 实验性功能
  experimental: {
    // 启用顶级await支持
    renderBuiltUrl(filename, { hostType }) {
      if (hostType === 'js') {
        return { js: `chrome.runtime.getURL("${filename}")` };
      } else {
        return { relative: true };
      }
    },
  },
});
