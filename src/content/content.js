/**
 * Chrome拼音标注插件 - Content Script
 * 负责在网页中进行DOM操作和拼音标注
 */

import { MessageTypes, DOMSelectors, CSSClasses, DisplayStyles } from '../utils/constants.js';
import { PinyinConverter } from '../core/PinyinConverter.js';
import { TooltipManager } from '../core/TooltipManager.js';
import { ContentAreaDetector } from '../core/ContentAreaDetector.js';

class ContentController {
  constructor() {
    this.isEnabled = false;
    this.currentSettings = null;
    this.processedNodes = new WeakSet();
    this.observer = null;
    this.isProcessing = false;

    // 初始化核心组件
    this.pinyinConverter = new PinyinConverter();
    this.tooltipManager = new TooltipManager(this.pinyinConverter);
    this.contentAreaDetector = new ContentAreaDetector();

    this.init();
  }

  /**
   * 初始化内容脚本
   */
  init() {
    try {
      // 监听来自后台脚本的消息
      chrome.runtime.onMessage.addListener(this.handleMessage.bind(this));

      // 监听DOM变化
      this.setupMutationObserver();

      console.log('Content script initialized');
    } catch (error) {
      console.error('Failed to initialize content script:', error);
    }
  }

  /**
   * 处理来自后台脚本的消息
   */
  async handleMessage(message, sender, sendResponse) {
    try {
      switch (message.type) {
        case MessageTypes.ENABLE_PINYIN:
          await this.enablePinyin(message.data);
          sendResponse({ success: true });
          break;

        case MessageTypes.DISABLE_PINYIN:
          await this.disablePinyin();
          sendResponse({ success: true });
          break;

        case MessageTypes.UPDATE_SETTINGS:
          await this.updateSettings(message.data);
          sendResponse({ success: true });
          break;

        default:
          sendResponse({ success: false, error: 'Unknown message type' });
      }
    } catch (error) {
      console.error('Error handling message:', error);
      sendResponse({ success: false, error: error.message });
    }
  }

  /**
   * 启用拼音标注
   */
  async enablePinyin(settings) {
    if (this.isProcessing) return;

    try {
      this.isProcessing = true;
      this.isEnabled = true;
      this.currentSettings = settings;

      // 更新拼音转换器设置
      this.pinyinConverter.setToneType(settings.toneType);
      this.pinyinConverter.setPolyphoneEnabled(settings.polyphone !== false);
      this.pinyinConverter.setRareWordOnlyMode(settings.rareWordOnly || false);

      // 根据显示样式决定是否启用悬浮提示
      if (settings.displayStyle === DisplayStyles.TOOLTIP || settings.enableHoverTooltip) {
        this.tooltipManager.enable(settings);
      }

      // 添加样式
      this.injectStyles();

      // 处理页面内容
      await this.processPageContent();

      // 开始监听DOM变化
      this.startObserving();

      console.log('Pinyin annotation enabled');
    } catch (error) {
      console.error('Failed to enable pinyin:', error);
      this.reportError('启用拼音标注失败', error);
    } finally {
      this.isProcessing = false;
    }
  }

  /**
   * 禁用拼音标注
   */
  async disablePinyin() {
    try {
      this.isEnabled = false;

      // 禁用悬浮提示
      this.tooltipManager.disable();

      // 停止监听DOM变化
      this.stopObserving();

      // 移除所有拼音标注
      this.removeAllAnnotations();

      // 移除样式
      this.removeStyles();

      console.log('Pinyin annotation disabled');
    } catch (error) {
      console.error('Failed to disable pinyin:', error);
    }
  }

  /**
   * 更新设置
   */
  async updateSettings(settings) {
    if (!this.isEnabled) return;

    try {
      const oldSettings = this.currentSettings;
      this.currentSettings = settings;

      // 更新拼音转换器设置
      this.pinyinConverter.setToneType(settings.toneType);
      this.pinyinConverter.setPolyphoneEnabled(settings.polyphone !== false);
      this.pinyinConverter.setRareWordOnlyMode(settings.rareWordOnly || false);

      // 更新悬浮提示设置
      if (settings.displayStyle === DisplayStyles.TOOLTIP || settings.enableHoverTooltip) {
        this.tooltipManager.enable(settings);
      } else {
        this.tooltipManager.disable();
      }

      // 如果显示样式发生变化，重新处理页面
      if (oldSettings?.displayStyle !== settings.displayStyle ||
        oldSettings?.toneType !== settings.toneType) {
        this.removeAllAnnotations();
        await this.processPageContent();
      }

      console.log('Settings updated');
    } catch (error) {
      console.error('Failed to update settings:', error);
    }
  }

  /**
   * 注入样式
   */
  injectStyles() {
    if (document.getElementById('pinyin-extension-styles')) return;

    const styleElement = document.createElement('style');
    styleElement.id = 'pinyin-extension-styles';
    styleElement.textContent = this.getStylesCSS();

    document.head.appendChild(styleElement);
  }

  /**
   * 移除样式
   */
  removeStyles() {
    const styleElement = document.getElementById('pinyin-extension-styles');
    if (styleElement) {
      styleElement.remove();
    }
  }

  /**
   * 获取样式CSS
   */
  getStylesCSS() {
    return `
      /* 拼音标注基础样式 */
      .${CSSClasses.PINYIN_CONTAINER} {
        display: inline;
      }
      
      /* Ruby样式 */
      .${CSSClasses.RUBY_STYLE} {
        ruby-align: center;
      }
      
      .${CSSClasses.RUBY_STYLE} rt {
        font-size: 0.7em;
        color: #1a73e8;
        font-weight: normal;
        line-height: 1;
        user-select: none;
      }
      
      /* 悬浮提示样式 */
      .${CSSClasses.TOOLTIP_STYLE} {
        position: relative;
        cursor: help;
        border-bottom: 1px dotted #1a73e8;
      }
      
      .${CSSClasses.TOOLTIP_STYLE}:hover::after {
        content: attr(data-pinyin);
        position: absolute;
        bottom: 100%;
        left: 50%;
        transform: translateX(-50%);
        background: rgba(0, 0, 0, 0.8);
        color: white;
        padding: 4px 8px;
        border-radius: 4px;
        font-size: 12px;
        white-space: nowrap;
        z-index: 10000;
        pointer-events: none;
      }
      
      .${CSSClasses.TOOLTIP_STYLE}:hover::before {
        content: '';
        position: absolute;
        bottom: 100%;
        left: 50%;
        transform: translateX(-50%) translateY(100%);
        border: 4px solid transparent;
        border-top-color: rgba(0, 0, 0, 0.8);
        z-index: 10000;
        pointer-events: none;
      }
      
      /* 括号样式 */
      .${CSSClasses.BRACKET_STYLE} .pinyin-bracket {
        color: #1a73e8;
        font-size: 0.8em;
        margin-left: 2px;
        user-select: none;
      }
      
      /* 处理中状态 */
      .${CSSClasses.PROCESSING} {
        opacity: 0.6;
      }
      
      /* 错误状态 */
      .${CSSClasses.ERROR} {
        background-color: #ffebee;
        border: 1px solid #f44336;
      }

      /* 生僻字特殊样式 */
      .pinyin-rare-char {
        background-color: rgba(255, 193, 7, 0.1);
        border-radius: 2px;
        position: relative;
      }

      .pinyin-rare-char.${CSSClasses.RUBY_STYLE} rt {
        color: #ff5722;
        font-weight: bold;
      }

      .pinyin-rare-char.${CSSClasses.TOOLTIP_STYLE} {
        border-bottom-color: #ff5722;
        border-bottom-width: 2px;
      }

      .pinyin-rare-char.${CSSClasses.BRACKET_STYLE} .pinyin-bracket {
        color: #ff5722;
        font-weight: bold;
      }
    `;
  }

  /**
   * 处理页面内容
   */
  async processPageContent() {
    if (!this.isEnabled || this.isProcessing) return;

    try {
      this.isProcessing = true;

      // 根据设置决定处理范围
      let targetElements;

      if (this.currentSettings.scope === 'smart') {
        // 智能识别内容区域
        targetElements = this.contentAreaDetector.getMainContentAreas();
      } else {
        // 处理整个页面
        targetElements = [document.body];
      }

      // 处理每个目标区域
      for (const element of targetElements) {
        if (element && this.contentAreaDetector.isContentArea(element)) {
          const textNodes = this.getTextNodes(element);
          await this.processTextNodes(textNodes);
        }
      }

    } catch (error) {
      console.error('Failed to process page content:', error);
      this.reportError('处理页面内容失败', error);
    } finally {
      this.isProcessing = false;
    }
  }

  /**
   * 获取文本节点
   */
  getTextNodes(container) {
    const textNodes = [];
    const walker = document.createTreeWalker(
      container,
      NodeFilter.SHOW_TEXT,
      {
        acceptNode: (node) => {
          // 跳过已处理的节点
          if (this.processedNodes.has(node)) {
            return NodeFilter.FILTER_REJECT;
          }

          // 跳过排除的元素
          const parent = node.parentElement;
          if (parent && this.isExcludedElement(parent)) {
            return NodeFilter.FILTER_REJECT;
          }

          // 跳过空白或无中文的文本
          const text = node.textContent.trim();
          if (!text || !/[\u4e00-\u9fff]/.test(text)) {
            return NodeFilter.FILTER_REJECT;
          }

          return NodeFilter.FILTER_ACCEPT;
        }
      }
    );

    let node;
    while ((node = walker.nextNode())) {
      textNodes.push(node);
    }

    return textNodes;
  }

  /**
   * 检查是否为排除的元素
   */
  isExcludedElement(element) {
    const tagName = element.tagName.toLowerCase();

    // 检查排除的标签
    if (DOMSelectors.EXCLUDED_ELEMENTS.includes(tagName)) {
      return true;
    }

    // 检查是否已经是拼音标注元素
    if (element.classList.contains(CSSClasses.PINYIN_CONTAINER) ||
      element.classList.contains(CSSClasses.PINYIN_ANNOTATION)) {
      return true;
    }

    // 检查是否在排除的父元素中
    let parent = element.parentElement;
    while (parent) {
      if (DOMSelectors.EXCLUDED_ELEMENTS.includes(parent.tagName.toLowerCase())) {
        return true;
      }
      parent = parent.parentElement;
    }

    return false;
  }

  /**
   * 批量处理文本节点
   */
  async processTextNodes(textNodes) {
    const batchSize = 10; // 每批处理的节点数

    for (let i = 0; i < textNodes.length; i += batchSize) {
      const batch = textNodes.slice(i, i + batchSize);

      // 处理当前批次
      for (const textNode of batch) {
        await this.processTextNode(textNode);
      }

      // 让出控制权，避免阻塞UI
      await new Promise(resolve => setTimeout(resolve, 0));
    }
  }

  /**
   * 处理单个文本节点
   */
  async processTextNode(textNode) {
    try {
      const text = textNode.textContent;
      if (!text || !/[\u4e00-\u9fff]/.test(text)) return;

      // 标记为已处理
      this.processedNodes.add(textNode);

      // 这里应该调用拼音转换功能
      // 目前先创建一个占位符实现
      const annotatedHTML = await this.createAnnotatedHTML(text);

      if (annotatedHTML !== text) {
        // 创建新的容器元素
        const container = document.createElement('span');
        container.className = CSSClasses.PINYIN_CONTAINER;
        container.innerHTML = annotatedHTML;

        // 替换原文本节点
        textNode.parentNode.replaceChild(container, textNode);
      }

    } catch (error) {
      console.error('Failed to process text node:', error);
    }
  }

  /**
   * 创建带拼音标注的HTML
   */
  async createAnnotatedHTML(text) {
    if (!this.currentSettings || !text) return text;

    // 使用拼音转换器处理文本
    const convertedResults = this.pinyinConverter.convertText(
      text,
      this.currentSettings.mode === 'word'
    );

    let html = '';

    for (const result of convertedResults) {
      if (result.isChinese && result.pinyin) {
        // 为生僻字添加特殊样式类
        const rareClass = result.isRare ? ' pinyin-rare-char' : '';

        // 根据显示样式生成HTML
        switch (this.currentSettings.displayStyle) {
          case DisplayStyles.RUBY:
            html += `<ruby class="${CSSClasses.RUBY_STYLE}${rareClass}">${result.text}<rt>${result.pinyin}</rt></ruby>`;
            break;

          case DisplayStyles.TOOLTIP:
            html += `<span class="${CSSClasses.TOOLTIP_STYLE}${rareClass}" data-pinyin="${result.pinyin}" data-rare="${result.isRare}">${result.text}</span>`;
            break;

          case DisplayStyles.BRACKET:
            html += `<span class="${CSSClasses.BRACKET_STYLE}${rareClass}">${result.text}<span class="pinyin-bracket">(${result.pinyin})</span></span>`;
            break;

          default:
            html += result.text;
        }
      } else {
        // 非中文字符或无拼音的字符直接添加
        html += result.text;
      }
    }

    return html;
  }



  /**
   * 移除所有拼音标注
   */
  removeAllAnnotations() {
    const annotatedElements = document.querySelectorAll(`.${CSSClasses.PINYIN_CONTAINER}`);

    annotatedElements.forEach(element => {
      // 获取原始文本
      const originalText = element.textContent;

      // 创建文本节点替换标注元素
      const textNode = document.createTextNode(originalText);
      element.parentNode.replaceChild(textNode, element);
    });

    // 清空已处理节点记录
    this.processedNodes = new WeakSet();
  }

  /**
   * 设置DOM变化监听器
   */
  setupMutationObserver() {
    this.observer = new MutationObserver((mutations) => {
      if (!this.isEnabled || this.isProcessing) return;

      let hasNewContent = false;

      mutations.forEach(mutation => {
        if (mutation.type === 'childList') {
          mutation.addedNodes.forEach(node => {
            if (node.nodeType === Node.TEXT_NODE ||
              (node.nodeType === Node.ELEMENT_NODE && node.textContent.trim())) {
              hasNewContent = true;
            }
          });
        }
      });

      if (hasNewContent) {
        // 延迟处理，避免频繁触发
        clearTimeout(this.processTimeout);
        this.processTimeout = setTimeout(() => {
          this.processPageContent();
        }, 500);
      }
    });
  }

  /**
   * 开始监听DOM变化
   */
  startObserving() {
    if (this.observer) {
      this.observer.observe(document.body, {
        childList: true,
        subtree: true,
        characterData: true
      });
    }
  }

  /**
   * 停止监听DOM变化
   */
  stopObserving() {
    if (this.observer) {
      this.observer.disconnect();
    }

    if (this.processTimeout) {
      clearTimeout(this.processTimeout);
    }
  }

  /**
   * 报告错误到后台脚本
   */
  reportError(message, error) {
    chrome.runtime.sendMessage({
      type: MessageTypes.ERROR_REPORT,
      data: {
        message,
        error: error.message,
        stack: error.stack,
        url: window.location.href,
        timestamp: Date.now()
      }
    });
  }
}

// 初始化内容控制器
new ContentController();
