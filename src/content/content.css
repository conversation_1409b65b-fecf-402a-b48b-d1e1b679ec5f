/**
 * Chrome拼音标注插件 - Content Script样式
 * 在网页中注入的拼音标注样式
 */

/* 防止样式冲突的命名空间 */
.pinyin-extension-content {
  /* 基础变量 */
  --pinyin-primary-color: #1a73e8;
  --pinyin-secondary-color: #5f6368;
  --pinyin-success-color: #137333;
  --pinyin-warning-color: #ea8600;
  --pinyin-error-color: #d93025;
  --pinyin-bg-tooltip: rgba(0, 0, 0, 0.8);
  --pinyin-text-white: #ffffff;
  --pinyin-border-radius: 4px;
  --pinyin-shadow: 0 2px 8px rgba(0, 0, 0, 0.15);
  --pinyin-transition: all 0.2s ease;
}

/* 拼音容器基础样式 */
.pinyin-container {
  display: inline !important;
  font-family: inherit !important;
  font-size: inherit !important;
  line-height: inherit !important;
  color: inherit !important;
  background: transparent !important;
  border: none !important;
  margin: 0 !important;
  padding: 0 !important;
}

/* Ruby标注样式 */
.pinyin-ruby {
  ruby-align: center !important;
  ruby-position: over !important;
}

.pinyin-ruby rt {
  font-size: 0.7em !important;
  color: var(--pinyin-primary-color) !important;
  font-weight: normal !important;
  line-height: 1 !important;
  user-select: none !important;
  pointer-events: none !important;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif !important;
  text-shadow: none !important;
  background: transparent !important;
  border: none !important;
  margin: 0 !important;
  padding: 0 !important;
  text-decoration: none !important;
  text-transform: none !important;
  letter-spacing: normal !important;
  word-spacing: normal !important;
}

/* 悬浮提示样式 */
.pinyin-tooltip {
  position: relative !important;
  cursor: help !important;
  border-bottom: 1px dotted var(--pinyin-primary-color) !important;
  text-decoration: none !important;
  background: transparent !important;
  color: inherit !important;
  font-family: inherit !important;
  font-size: inherit !important;
  font-weight: inherit !important;
  margin: 0 !important;
  padding: 0 !important;
  display: inline !important;
  transition: var(--pinyin-transition) !important;
}

.pinyin-tooltip:hover {
  border-bottom-color: var(--pinyin-primary-color) !important;
  border-bottom-style: solid !important;
}

/* 悬浮提示框 */
.pinyin-tooltip::after {
  content: attr(data-pinyin) !important;
  position: absolute !important;
  bottom: 100% !important;
  left: 50% !important;
  transform: translateX(-50%) translateY(-4px) !important;
  background: var(--pinyin-bg-tooltip) !important;
  color: var(--pinyin-text-white) !important;
  padding: 4px 8px !important;
  border-radius: var(--pinyin-border-radius) !important;
  font-size: 12px !important;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif !important;
  font-weight: normal !important;
  line-height: 1.2 !important;
  white-space: nowrap !important;
  z-index: 10000 !important;
  pointer-events: none !important;
  opacity: 0 !important;
  visibility: hidden !important;
  transition: opacity 0.2s ease, visibility 0.2s ease, transform 0.2s ease !important;
  box-shadow: var(--pinyin-shadow) !important;
  text-shadow: none !important;
  text-decoration: none !important;
  text-transform: none !important;
  letter-spacing: normal !important;
  word-spacing: normal !important;
  border: none !important;
  margin: 0 !important;
}

.pinyin-tooltip:hover::after {
  opacity: 1 !important;
  visibility: visible !important;
  transform: translateX(-50%) translateY(-8px) !important;
}

/* 悬浮提示箭头 */
.pinyin-tooltip::before {
  content: '' !important;
  position: absolute !important;
  bottom: 100% !important;
  left: 50% !important;
  transform: translateX(-50%) !important;
  border: 4px solid transparent !important;
  border-top-color: var(--pinyin-bg-tooltip) !important;
  z-index: 10001 !important;
  pointer-events: none !important;
  opacity: 0 !important;
  visibility: hidden !important;
  transition: opacity 0.2s ease, visibility 0.2s ease !important;
  margin: 0 !important;
  padding: 0 !important;
}

.pinyin-tooltip:hover::before {
  opacity: 1 !important;
  visibility: visible !important;
}

/* 括号拼音样式 */
.pinyin-bracket {
  display: inline !important;
  font-family: inherit !important;
  font-size: inherit !important;
  line-height: inherit !important;
  color: inherit !important;
  background: transparent !important;
  border: none !important;
  margin: 0 !important;
  padding: 0 !important;
}

.pinyin-bracket .pinyin-bracket-text {
  color: var(--pinyin-primary-color) !important;
  font-size: 0.8em !important;
  margin-left: 2px !important;
  user-select: none !important;
  pointer-events: none !important;
  font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, 'Helvetica Neue', Arial, sans-serif !important;
  font-weight: normal !important;
  line-height: inherit !important;
  text-shadow: none !important;
  background: transparent !important;
  border: none !important;
  padding: 0 !important;
  text-decoration: none !important;
  text-transform: none !important;
  letter-spacing: normal !important;
  word-spacing: normal !important;
}

/* 主题颜色变体 */
.pinyin-theme-blue .pinyin-ruby rt,
.pinyin-theme-blue .pinyin-tooltip,
.pinyin-theme-blue .pinyin-bracket .pinyin-bracket-text {
  color: #1a73e8 !important;
}

.pinyin-theme-green .pinyin-ruby rt,
.pinyin-theme-green .pinyin-tooltip,
.pinyin-theme-green .pinyin-bracket .pinyin-bracket-text {
  color: #137333 !important;
}

.pinyin-theme-red .pinyin-ruby rt,
.pinyin-theme-red .pinyin-tooltip,
.pinyin-theme-red .pinyin-bracket .pinyin-bracket-text {
  color: #d93025 !important;
}

.pinyin-theme-gray .pinyin-ruby rt,
.pinyin-theme-gray .pinyin-tooltip,
.pinyin-theme-gray .pinyin-bracket .pinyin-bracket-text {
  color: #5f6368 !important;
}

/* 字体大小变体 */
.pinyin-size-small .pinyin-ruby rt {
  font-size: 0.6em !important;
}

.pinyin-size-small .pinyin-bracket .pinyin-bracket-text {
  font-size: 0.7em !important;
}

.pinyin-size-medium .pinyin-ruby rt {
  font-size: 0.7em !important;
}

.pinyin-size-medium .pinyin-bracket .pinyin-bracket-text {
  font-size: 0.8em !important;
}

.pinyin-size-large .pinyin-ruby rt {
  font-size: 0.8em !important;
}

.pinyin-size-large .pinyin-bracket .pinyin-bracket-text {
  font-size: 0.9em !important;
}

/* 处理状态样式 */
.pinyin-processing {
  opacity: 0.6 !important;
  transition: opacity 0.3s ease !important;
}

.pinyin-processed {
  /* 标记已处理的元素，但不改变外观 */
}

.pinyin-error {
  background-color: rgba(239, 68, 68, 0.1) !important;
  border: 1px solid rgba(239, 68, 68, 0.3) !important;
  border-radius: 2px !important;
  padding: 1px 2px !important;
}

/* 动画效果 */
@keyframes pinyin-fade-in {
  from {
    opacity: 0;
    transform: translateY(-2px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.pinyin-fade-in {
  animation: pinyin-fade-in 0.3s ease-out !important;
}

/* 响应式适配 */
@media (max-width: 768px) {
  .pinyin-ruby rt {
    font-size: 0.6em !important;
  }
  
  .pinyin-tooltip::after {
    font-size: 11px !important;
    padding: 3px 6px !important;
  }
  
  .pinyin-bracket .pinyin-bracket-text {
    font-size: 0.75em !important;
  }
}

/* 高对比度模式适配 */
@media (prefers-contrast: high) {
  .pinyin-ruby rt,
  .pinyin-tooltip,
  .pinyin-bracket .pinyin-bracket-text {
    color: #000000 !important;
    font-weight: bold !important;
  }
  
  .pinyin-tooltip {
    border-bottom: 2px solid #000000 !important;
  }
  
  .pinyin-tooltip::after {
    background: #000000 !important;
    color: #ffffff !important;
    border: 1px solid #ffffff !important;
  }
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  .pinyin-extension-content {
    --pinyin-primary-color: #8ab4f8;
    --pinyin-secondary-color: #9aa0a6;
    --pinyin-bg-tooltip: rgba(255, 255, 255, 0.9);
    --pinyin-text-white: #202124;
  }
  
  .pinyin-tooltip::after {
    background: var(--pinyin-bg-tooltip) !important;
    color: var(--pinyin-text-white) !important;
  }
  
  .pinyin-tooltip::before {
    border-top-color: var(--pinyin-bg-tooltip) !important;
  }
}

/* 打印样式 */
@media print {
  .pinyin-tooltip::after,
  .pinyin-tooltip::before {
    display: none !important;
  }
  
  .pinyin-tooltip {
    border-bottom: none !important;
    cursor: default !important;
  }
  
  .pinyin-ruby rt {
    color: #000000 !important;
  }
  
  .pinyin-bracket .pinyin-bracket-text {
    color: #666666 !important;
  }
}

/* 防止与网站样式冲突 */
.pinyin-container,
.pinyin-ruby,
.pinyin-tooltip,
.pinyin-bracket {
  all: unset !important;
  display: inline !important;
  font-family: inherit !important;
  font-size: inherit !important;
  line-height: inherit !important;
  color: inherit !important;
}

/* 确保拼音标注不影响页面布局 */
.pinyin-container {
  vertical-align: baseline !important;
  text-align: inherit !important;
  white-space: inherit !important;
  word-break: inherit !important;
  word-wrap: inherit !important;
  overflow-wrap: inherit !important;
}
