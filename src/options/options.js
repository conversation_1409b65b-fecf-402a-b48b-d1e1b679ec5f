/**
 * Chrome拼音标注插件 - Options页面脚本
 * 处理设置页面的交互逻辑和配置管理
 */

import { MessageTypes, DefaultSettings } from '../utils/constants.js';

class OptionsController {
  constructor() {
    this.currentSettings = null;
    this.isLoading = false;
    this.hasUnsavedChanges = false;

    this.init();
  }

  /**
   * 初始化设置页面控制器
   */
  async init() {
    try {
      // 绑定事件监听器
      this.bindEventListeners();

      // 加载当前设置
      await this.loadSettings();

      // 监听页面卸载事件
      this.bindUnloadEvents();

      console.log('Options controller initialized');
    } catch (error) {
      console.error('Failed to initialize options controller:', error);
      this.showToast('初始化失败，请刷新页面重试', 'error');
    }
  }

  /**
   * 绑定事件监听器
   */
  bindEventListeners() {
    // 保存设置按钮
    const saveBtn = document.getElementById('save-settings');
    if (saveBtn) {
      saveBtn.addEventListener('click', this.handleSaveSettings.bind(this));
    }

    // 重置设置按钮
    const resetBtn = document.getElementById('reset-settings');
    if (resetBtn) {
      resetBtn.addEventListener('click', this.handleResetSettings.bind(this));
    }

    // 导出设置按钮
    const exportBtn = document.getElementById('export-settings');
    if (exportBtn) {
      exportBtn.addEventListener('click', this.handleExportSettings.bind(this));
    }

    // 导入设置按钮
    const importBtn = document.getElementById('import-settings');
    if (importBtn) {
      importBtn.addEventListener('click', this.handleImportSettings.bind(this));
    }

    // 清理缓存按钮
    const clearCacheBtn = document.getElementById('clear-cache');
    if (clearCacheBtn) {
      clearCacheBtn.addEventListener('click', this.handleClearCache.bind(this));
    }

    // 添加排除网站按钮
    const addExcludeBtn = document.getElementById('add-exclude-site');
    if (addExcludeBtn) {
      addExcludeBtn.addEventListener('click', this.handleAddExcludeSite.bind(this));
    }

    // 帮助链接
    const helpLink = document.getElementById('help-link');
    if (helpLink) {
      helpLink.addEventListener('click', this.handleHelpLink.bind(this));
    }

    // 监听表单变化
    this.bindFormChangeListeners();
  }

  /**
   * 绑定表单变化监听器
   */
  bindFormChangeListeners() {
    const formElements = document.querySelectorAll('input, select, textarea');
    formElements.forEach(element => {
      element.addEventListener('change', () => {
        this.hasUnsavedChanges = true;
        this.updateSaveButtonState();
      });
    });
  }

  /**
   * 绑定页面卸载事件
   */
  bindUnloadEvents() {
    window.addEventListener('beforeunload', (e) => {
      if (this.hasUnsavedChanges) {
        e.preventDefault();
        e.returnValue = '您有未保存的更改，确定要离开吗？';
      }
    });
  }

  /**
   * 加载设置
   */
  async loadSettings() {
    try {
      this.setLoading(true);

      const response = await this.sendMessage({
        type: MessageTypes.GET_STATUS
      });

      if (response.success) {
        this.currentSettings = response.data;
        this.updateUI();
        this.hasUnsavedChanges = false;
        this.updateSaveButtonState();
      } else {
        throw new Error(response.error || '加载设置失败');
      }
    } catch (error) {
      console.error('Failed to load settings:', error);
      this.showToast('加载设置失败', 'error');
    } finally {
      this.setLoading(false);
    }
  }

  /**
   * 更新UI界面
   */
  updateUI() {
    if (!this.currentSettings) return;

    // 更新各个设置项
    this.updateCheckbox('auto-enable', this.currentSettings.autoEnable);
    this.updateCheckbox('rare-words-only', this.currentSettings.rareWordOnly);
    this.updateCheckbox('enable-hover-tooltip', this.currentSettings.enableHoverTooltip);
    this.updateCheckbox('enable-polyphone', this.currentSettings.polyphone);
    this.updateSelect('annotation-scope', this.currentSettings.scope);
    this.updateSelect('display-style', this.currentSettings.displayStyle);
    this.updateSelect('pinyin-format', this.currentSettings.toneType);
    this.updateSelect('font-size', this.currentSettings.fontSize);
    this.updateSelect('color-theme', this.currentSettings.colorTheme);
    this.updateRange('word-frequency-threshold', this.currentSettings.wordFrequencyThreshold);

    // 更新排除网站列表
    this.updateExcludeList(this.currentSettings.excludeDomains);
  }

  /**
   * 更新复选框
   */
  updateCheckbox(id, value) {
    const element = document.getElementById(id);
    if (element) {
      element.checked = value;
    }
  }

  /**
   * 更新选择框
   */
  updateSelect(id, value) {
    const element = document.getElementById(id);
    if (element) {
      element.value = value;
    }
  }

  /**
   * 更新范围滑块
   */
  updateRange(id, value) {
    const element = document.getElementById(id);
    if (element) {
      element.value = value;
    }
  }

  /**
   * 更新排除网站列表
   */
  updateExcludeList(domains) {
    const listElement = document.getElementById('exclude-list');
    if (!listElement) return;

    listElement.innerHTML = '';

    domains.forEach((domain, index) => {
      const listItem = document.createElement('li');
      listItem.className = 'exclude-item';
      listItem.innerHTML = `
        <span class="exclude-domain">${domain}</span>
        <button class="btn btn-small btn-danger" data-index="${index}">删除</button>
      `;

      // 绑定删除事件
      const deleteBtn = listItem.querySelector('button');
      deleteBtn.addEventListener('click', () => {
        this.handleRemoveExcludeSite(index);
      });

      listElement.appendChild(listItem);
    });
  }

  /**
   * 处理保存设置
   */
  async handleSaveSettings() {
    if (this.isLoading) return;

    try {
      this.setLoading(true);

      // 收集表单数据
      const settings = this.collectFormData();

      // 发送保存请求
      const response = await this.sendMessage({
        type: MessageTypes.UPDATE_SETTINGS,
        data: settings
      });

      if (response.success) {
        this.currentSettings = settings;
        this.hasUnsavedChanges = false;
        this.updateSaveButtonState();
        this.showToast('设置保存成功', 'success');
      } else {
        throw new Error(response.error || '保存设置失败');
      }
    } catch (error) {
      console.error('Failed to save settings:', error);
      this.showToast('保存设置失败', 'error');
    } finally {
      this.setLoading(false);
    }
  }

  /**
   * 收集表单数据
   */
  collectFormData() {
    return {
      ...this.currentSettings,
      autoEnable: this.getCheckboxValue('auto-enable'),
      rareWordOnly: this.getCheckboxValue('rare-words-only'),
      enableHoverTooltip: this.getCheckboxValue('enable-hover-tooltip'),
      polyphone: this.getCheckboxValue('enable-polyphone'),
      scope: this.getSelectValue('annotation-scope'),
      displayStyle: this.getSelectValue('display-style'),
      toneType: this.getSelectValue('pinyin-format'),
      fontSize: this.getSelectValue('font-size'),
      colorTheme: this.getSelectValue('color-theme'),
      wordFrequencyThreshold: parseInt(this.getRangeValue('word-frequency-threshold')),
      excludeDomains: this.currentSettings.excludeDomains || []
    };
  }

  /**
   * 获取复选框值
   */
  getCheckboxValue(id) {
    const element = document.getElementById(id);
    return element ? element.checked : false;
  }

  /**
   * 获取选择框值
   */
  getSelectValue(id) {
    const element = document.getElementById(id);
    return element ? element.value : '';
  }

  /**
   * 获取范围滑块值
   */
  getRangeValue(id) {
    const element = document.getElementById(id);
    return element ? element.value : 0;
  }

  /**
   * 处理重置设置
   */
  async handleResetSettings() {
    const confirmed = await this.showConfirmDialog(
      '重置设置',
      '确定要重置所有设置为默认值吗？此操作不可撤销。'
    );

    if (!confirmed) return;

    try {
      this.setLoading(true);

      const response = await this.sendMessage({
        type: MessageTypes.RESET_SETTINGS
      });

      if (response.success) {
        this.currentSettings = DefaultSettings;
        this.updateUI();
        this.hasUnsavedChanges = false;
        this.updateSaveButtonState();
        this.showToast('设置已重置为默认值', 'success');
      } else {
        throw new Error(response.error || '重置设置失败');
      }
    } catch (error) {
      console.error('Failed to reset settings:', error);
      this.showToast('重置设置失败', 'error');
    } finally {
      this.setLoading(false);
    }
  }

  /**
   * 处理导出设置
   */
  async handleExportSettings() {
    try {
      const dataStr = JSON.stringify(this.currentSettings, null, 2);
      const dataBlob = new Blob([dataStr], { type: 'application/json' });

      const url = URL.createObjectURL(dataBlob);
      const link = document.createElement('a');
      link.href = url;
      link.download = `pinyin-extension-settings-${new Date().toISOString().split('T')[0]}.json`;
      link.click();

      URL.revokeObjectURL(url);
      this.showToast('设置导出成功', 'success');
    } catch (error) {
      console.error('Failed to export settings:', error);
      this.showToast('导出设置失败', 'error');
    }
  }

  /**
   * 处理导入设置
   */
  handleImportSettings() {
    const input = document.createElement('input');
    input.type = 'file';
    input.accept = '.json';

    input.onchange = async (e) => {
      const file = e.target.files[0];
      if (!file) return;

      try {
        const text = await file.text();
        const settings = JSON.parse(text);

        // 验证设置格式
        if (typeof settings !== 'object') {
          throw new Error('无效的设置文件格式');
        }

        // 合并设置
        this.currentSettings = { ...DefaultSettings, ...settings };
        this.updateUI();
        this.hasUnsavedChanges = true;
        this.updateSaveButtonState();

        this.showToast('设置导入成功，请保存设置', 'success');
      } catch (error) {
        console.error('Failed to import settings:', error);
        this.showToast('导入设置失败：' + error.message, 'error');
      }
    };

    input.click();
  }

  /**
   * 处理清理缓存
   */
  async handleClearCache() {
    const confirmed = await this.showConfirmDialog(
      '清理缓存',
      '确定要清理所有缓存数据吗？这将删除拼音转换和分词缓存。'
    );

    if (!confirmed) return;

    try {
      // 这里应该调用后台脚本的清理缓存功能
      // 目前先显示成功消息
      this.showToast('缓存清理成功', 'success');
    } catch (error) {
      console.error('Failed to clear cache:', error);
      this.showToast('清理缓存失败', 'error');
    }
  }

  /**
   * 处理添加排除网站
   */
  handleAddExcludeSite() {
    const input = document.getElementById('exclude-site-input');
    if (!input) return;

    const domain = input.value.trim();
    if (!domain) {
      this.showToast('请输入有效的域名', 'warning');
      return;
    }

    // 验证域名格式
    if (!/^[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/.test(domain)) {
      this.showToast('域名格式不正确', 'warning');
      return;
    }

    // 检查是否已存在
    if (this.currentSettings.excludeDomains.includes(domain)) {
      this.showToast('该域名已在排除列表中', 'warning');
      return;
    }

    // 添加到列表
    this.currentSettings.excludeDomains.push(domain);
    this.updateExcludeList(this.currentSettings.excludeDomains);

    // 清空输入框
    input.value = '';

    // 标记有未保存的更改
    this.hasUnsavedChanges = true;
    this.updateSaveButtonState();
  }

  /**
   * 处理移除排除网站
   */
  handleRemoveExcludeSite(index) {
    this.currentSettings.excludeDomains.splice(index, 1);
    this.updateExcludeList(this.currentSettings.excludeDomains);

    this.hasUnsavedChanges = true;
    this.updateSaveButtonState();
  }

  /**
   * 处理帮助链接
   */
  handleHelpLink() {
    const helpUrl = chrome.runtime.getURL('help.html');
    chrome.tabs.create({ url: helpUrl });
  }

  /**
   * 更新保存按钮状态
   */
  updateSaveButtonState() {
    const saveBtn = document.getElementById('save-settings');
    if (saveBtn) {
      saveBtn.disabled = !this.hasUnsavedChanges || this.isLoading;
      saveBtn.textContent = this.hasUnsavedChanges ? '保存设置 *' : '保存设置';
    }
  }

  /**
   * 设置加载状态
   */
  setLoading(loading) {
    this.isLoading = loading;

    // 禁用/启用所有交互元素
    const interactiveElements = document.querySelectorAll('button, input, select, textarea');
    interactiveElements.forEach(element => {
      element.disabled = loading;
    });
  }

  /**
   * 显示确认对话框
   */
  async showConfirmDialog(title, message) {
    return new Promise((resolve) => {
      const modal = document.getElementById('modal-overlay');
      const titleElement = document.getElementById('modal-title');
      const messageElement = document.getElementById('modal-message');
      const confirmBtn = document.getElementById('modal-confirm');
      const cancelBtn = document.getElementById('modal-cancel');

      if (!modal || !titleElement || !messageElement || !confirmBtn || !cancelBtn) {
        resolve(false);
        return;
      }

      titleElement.textContent = title;
      messageElement.textContent = message;
      modal.removeAttribute('hidden');

      const handleConfirm = () => {
        modal.setAttribute('hidden', '');
        cleanup();
        resolve(true);
      };

      const handleCancel = () => {
        modal.setAttribute('hidden', '');
        cleanup();
        resolve(false);
      };

      const cleanup = () => {
        confirmBtn.removeEventListener('click', handleConfirm);
        cancelBtn.removeEventListener('click', handleCancel);
      };

      confirmBtn.addEventListener('click', handleConfirm);
      cancelBtn.addEventListener('click', handleCancel);
    });
  }

  /**
   * 显示提示消息
   */
  showToast(message, type = 'info') {
    const toast = document.getElementById('toast');
    const icon = document.getElementById('toast-icon');
    const messageElement = document.getElementById('toast-message');

    if (!toast || !icon || !messageElement) return;

    // 设置图标和样式
    const icons = {
      success: '✓',
      error: '✗',
      warning: '⚠',
      info: 'ℹ'
    };

    icon.textContent = icons[type] || icons.info;
    messageElement.textContent = message;

    // 设置样式类
    toast.className = `toast toast-${type}`;
    toast.removeAttribute('hidden');

    // 3秒后自动隐藏
    setTimeout(() => {
      toast.setAttribute('hidden', '');
    }, 3000);
  }

  /**
   * 发送消息到后台脚本
   */
  async sendMessage(message) {
    return new Promise((resolve) => {
      chrome.runtime.sendMessage(message, (response) => {
        if (chrome.runtime.lastError) {
          resolve({
            success: false,
            error: chrome.runtime.lastError.message
          });
        } else {
          resolve(response || { success: false, error: 'No response' });
        }
      });
    });
  }
}

// 当DOM加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
  new OptionsController();
});
