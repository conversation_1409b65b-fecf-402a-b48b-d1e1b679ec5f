/**
 * Chrome拼音标注插件 - Options页面样式
 * 基于Material Design的设置页面样式
 */

/* 导入基础变量 */
@import '../popup/popup.css';

/* Options页面特定样式 */
.options-container {
  max-width: 800px;
  margin: 0 auto;
  padding: var(--spacing-lg);
  font-family: '<PERSON><PERSON>', 'Noto Sans SC', sans-serif;
  background: var(--bg-primary);
  min-height: 100vh;
}

/* 页面标题 */
.options-header {
  margin-bottom: var(--spacing-xl);
  padding-bottom: var(--spacing-lg);
  border-bottom: 1px solid var(--border-color);
}

.header-content {
  display: flex;
  align-items: center;
  gap: var(--spacing-md);
}

.header-icon {
  width: 48px;
  height: 48px;
}

.header-title {
  font-size: 28px;
  font-weight: 500;
  color: var(--text-primary);
  margin: 0;
}

.header-subtitle {
  font-size: 16px;
  color: var(--text-secondary);
  margin: 4px 0 0 0;
}

/* 主要内容 */
.options-main {
  margin-bottom: var(--spacing-xl);
}

/* 设置区域 */
.settings-section {
  margin-bottom: var(--spacing-xl);
  background: white;
  border-radius: var(--border-radius-md);
  padding: var(--spacing-lg);
  box-shadow: var(--shadow-sm);
  border: 1px solid var(--border-color);
}

.section-title {
  font-size: 20px;
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: var(--spacing-lg);
  padding-bottom: var(--spacing-sm);
  border-bottom: 2px solid var(--primary-color);
}

/* 设置网格 */
.settings-grid {
  display: grid;
  gap: var(--spacing-lg);
}

.setting-item {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.setting-label {
  font-size: 16px;
  font-weight: 500;
  color: var(--text-primary);
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.setting-description {
  font-size: 14px;
  color: var(--text-secondary);
  margin: 0;
  line-height: 1.5;
}

/* 范围滑块 */
.range-container {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.range-input {
  width: 100%;
  height: 6px;
  border-radius: 3px;
  background: var(--border-color);
  outline: none;
  -webkit-appearance: none;
}

.range-input::-webkit-slider-thumb {
  -webkit-appearance: none;
  appearance: none;
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: var(--primary-color);
  cursor: pointer;
  box-shadow: var(--shadow-sm);
}

.range-input::-moz-range-thumb {
  width: 20px;
  height: 20px;
  border-radius: 50%;
  background: var(--primary-color);
  cursor: pointer;
  border: none;
  box-shadow: var(--shadow-sm);
}

.range-labels {
  display: flex;
  justify-content: space-between;
  font-size: 12px;
  color: var(--text-secondary);
}

/* 排除网站 */
.exclude-sites {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-md);
}

.input-group {
  display: flex;
  gap: var(--spacing-sm);
}

.text-input {
  flex: 1;
  padding: var(--spacing-sm) var(--spacing-md);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-sm);
  font-size: 14px;
  color: var(--text-primary);
}

.text-input:focus {
  outline: none;
  border-color: var(--border-focus);
  box-shadow: 0 0 0 2px rgba(26, 115, 232, 0.2);
}

.exclude-list {
  list-style: none;
  padding: 0;
  margin: 0;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-sm);
}

.exclude-item {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-sm) var(--spacing-md);
  background: var(--bg-secondary);
  border-radius: var(--border-radius-sm);
  border: 1px solid var(--border-color);
}

.exclude-domain {
  font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
  font-size: 14px;
  color: var(--text-primary);
}

/* 按钮组 */
.button-group {
  display: flex;
  gap: var(--spacing-sm);
  flex-wrap: wrap;
}

.btn-small {
  padding: 4px 8px;
  font-size: 12px;
}

.btn-danger {
  background: var(--error-color);
  color: white;
  border-color: var(--error-color);
}

.btn-danger:hover {
  background: #c62828;
  border-color: #c62828;
}

/* 底部操作 */
.options-footer {
  padding-top: var(--spacing-lg);
  border-top: 1px solid var(--border-color);
  display: flex;
  flex-direction: column;
  gap: var(--spacing-lg);
}

.footer-actions {
  display: flex;
  gap: var(--spacing-md);
  justify-content: center;
  flex-wrap: wrap;
}

.footer-info {
  text-align: center;
  color: var(--text-secondary);
  font-size: 12px;
}

.version-info,
.copyright {
  margin: 2px 0;
}

/* 模态框 */
.modal-overlay {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.5);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.modal-dialog {
  background: white;
  border-radius: var(--border-radius-md);
  box-shadow: var(--shadow-lg);
  max-width: 400px;
  width: 90%;
  max-height: 80vh;
  overflow: auto;
}

.modal-header {
  padding: var(--spacing-lg);
  border-bottom: 1px solid var(--border-color);
}

.modal-title {
  font-size: 18px;
  font-weight: 500;
  color: var(--text-primary);
  margin: 0;
}

.modal-body {
  padding: var(--spacing-lg);
}

.modal-message {
  font-size: 14px;
  color: var(--text-primary);
  line-height: 1.5;
  margin: 0;
}

.modal-footer {
  padding: var(--spacing-lg);
  border-top: 1px solid var(--border-color);
  display: flex;
  gap: var(--spacing-sm);
  justify-content: flex-end;
}

/* Toast提示 */
.toast {
  position: fixed;
  bottom: var(--spacing-lg);
  right: var(--spacing-lg);
  background: white;
  border-radius: var(--border-radius-sm);
  box-shadow: var(--shadow-md);
  padding: var(--spacing-md);
  border-left: 4px solid var(--primary-color);
  z-index: 1001;
  animation: slideInRight 0.3s ease-out;
  max-width: 300px;
}

.toast-success {
  border-left-color: var(--success-color);
}

.toast-error {
  border-left-color: var(--error-color);
}

.toast-warning {
  border-left-color: var(--warning-color);
}

.toast-content {
  display: flex;
  align-items: center;
  gap: var(--spacing-sm);
}

.toast-icon {
  font-size: 16px;
  font-weight: bold;
}

.toast-success .toast-icon {
  color: var(--success-color);
}

.toast-error .toast-icon {
  color: var(--error-color);
}

.toast-warning .toast-icon {
  color: var(--warning-color);
}

.toast-message {
  font-size: 14px;
  color: var(--text-primary);
  flex: 1;
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(100%);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .options-container {
    padding: var(--spacing-md);
  }
  
  .settings-section {
    padding: var(--spacing-md);
  }
  
  .header-content {
    flex-direction: column;
    text-align: center;
  }
  
  .footer-actions {
    flex-direction: column;
  }
  
  .input-group {
    flex-direction: column;
  }
  
  .modal-dialog {
    margin: var(--spacing-md);
  }
}
