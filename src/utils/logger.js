/**
 * Chrome拼音标注插件 - 日志管理器
 * 提供统一的日志记录功能
 */

import { LogLevels } from './constants.js';

export class Logger {
  constructor(module = 'Unknown', level = LogLevels.INFO) {
    this.module = module;
    this.level = level;
    this.levels = [LogLevels.DEBUG, LogLevels.INFO, LogLevels.WARN, LogLevels.ERROR];
    this.colors = {
      [LogLevels.DEBUG]: '#6B7280',
      [LogLevels.INFO]: '#3B82F6',
      [LogLevels.WARN]: '#F59E0B',
      [LogLevels.ERROR]: '#EF4444'
    };
  }

  /**
   * 检查是否应该记录指定级别的日志
   * @param {string} level - 日志级别
   * @returns {boolean}
   */
  shouldLog(level) {
    const currentLevelIndex = this.levels.indexOf(this.level);
    const targetLevelIndex = this.levels.indexOf(level);
    return targetLevelIndex >= currentLevelIndex;
  }

  /**
   * 记录日志
   * @param {string} level - 日志级别
   * @param {string} message - 日志消息
   * @param {any} data - 附加数据
   */
  log(level, message, data = null) {
    if (!this.shouldLog(level)) {
      return;
    }

    const timestamp = new Date().toISOString();
    const logMessage = `[${timestamp}] [${this.module}] ${message}`;
    
    // 根据级别选择console方法
    const consoleMethod = this.getConsoleMethod(level);
    const color = this.colors[level];

    if (data) {
      consoleMethod(
        `%c${logMessage}`,
        `color: ${color}; font-weight: bold;`,
        data
      );
    } else {
      consoleMethod(
        `%c${logMessage}`,
        `color: ${color}; font-weight: bold;`
      );
    }

    // 在生产环境中可以将日志发送到远程服务器
    this.saveLog(level, message, data);
  }

  /**
   * 获取对应的console方法
   * @param {string} level - 日志级别
   * @returns {Function}
   */
  getConsoleMethod(level) {
    switch (level) {
      case LogLevels.DEBUG:
        return console.debug;
      case LogLevels.INFO:
        return console.info;
      case LogLevels.WARN:
        return console.warn;
      case LogLevels.ERROR:
        return console.error;
      default:
        return console.log;
    }
  }

  /**
   * 保存日志到本地存储
   * @param {string} level - 日志级别
   * @param {string} message - 日志消息
   * @param {any} data - 附加数据
   */
  async saveLog(level, message, data) {
    try {
      // 只保存警告和错误级别的日志
      if (level !== LogLevels.WARN && level !== LogLevels.ERROR) {
        return;
      }

      const logEntry = {
        timestamp: Date.now(),
        level,
        module: this.module,
        message,
        data: data ? JSON.stringify(data) : null,
        userAgent: navigator.userAgent,
        url: window.location?.href || 'unknown'
      };

      // 获取现有日志
      const result = await chrome.storage.local.get('logs');
      const logs = result.logs || [];

      // 添加新日志
      logs.push(logEntry);

      // 保持最近100条日志
      if (logs.length > 100) {
        logs.splice(0, logs.length - 100);
      }

      // 保存到存储
      await chrome.storage.local.set({ logs });
    } catch (error) {
      console.error('Failed to save log:', error);
    }
  }

  /**
   * 调试级别日志
   * @param {string} message - 日志消息
   * @param {any} data - 附加数据
   */
  debug(message, data) {
    this.log(LogLevels.DEBUG, message, data);
  }

  /**
   * 信息级别日志
   * @param {string} message - 日志消息
   * @param {any} data - 附加数据
   */
  info(message, data) {
    this.log(LogLevels.INFO, message, data);
  }

  /**
   * 警告级别日志
   * @param {string} message - 日志消息
   * @param {any} data - 附加数据
   */
  warn(message, data) {
    this.log(LogLevels.WARN, message, data);
  }

  /**
   * 错误级别日志
   * @param {string} message - 日志消息
   * @param {any} data - 附加数据
   */
  error(message, data) {
    this.log(LogLevels.ERROR, message, data);
  }

  /**
   * 设置日志级别
   * @param {string} level - 新的日志级别
   */
  setLevel(level) {
    if (this.levels.includes(level)) {
      this.level = level;
      this.info(`Log level changed to ${level}`);
    } else {
      this.warn(`Invalid log level: ${level}`);
    }
  }

  /**
   * 创建子模块日志器
   * @param {string} subModule - 子模块名称
   * @returns {Logger}
   */
  createSubLogger(subModule) {
    return new Logger(`${this.module}:${subModule}`, this.level);
  }

  /**
   * 获取保存的日志
   * @returns {Promise<Array>}
   */
  static async getSavedLogs() {
    try {
      const result = await chrome.storage.local.get('logs');
      return result.logs || [];
    } catch (error) {
      console.error('Failed to get saved logs:', error);
      return [];
    }
  }

  /**
   * 清理保存的日志
   * @returns {Promise<void>}
   */
  static async clearSavedLogs() {
    try {
      await chrome.storage.local.remove('logs');
      console.info('Saved logs cleared');
    } catch (error) {
      console.error('Failed to clear saved logs:', error);
    }
  }

  /**
   * 导出日志
   * @returns {Promise<string>}
   */
  static async exportLogs() {
    try {
      const logs = await Logger.getSavedLogs();
      const exportData = {
        exportTime: new Date().toISOString(),
        version: '1.0.0',
        logs: logs.map(log => ({
          ...log,
          timestamp: new Date(log.timestamp).toISOString()
        }))
      };
      
      return JSON.stringify(exportData, null, 2);
    } catch (error) {
      console.error('Failed to export logs:', error);
      return '{}';
    }
  }

  /**
   * 性能计时器
   * @param {string} label - 计时器标签
   * @returns {Function} 结束计时的函数
   */
  time(label) {
    const startTime = performance.now();
    this.debug(`Timer started: ${label}`);
    
    return () => {
      const endTime = performance.now();
      const duration = endTime - startTime;
      this.debug(`Timer ended: ${label}`, { duration: `${duration.toFixed(2)}ms` });
      return duration;
    };
  }

  /**
   * 记录函数执行时间
   * @param {Function} fn - 要执行的函数
   * @param {string} label - 标签
   * @returns {any} 函数执行结果
   */
  async timeAsync(fn, label) {
    const endTimer = this.time(label);
    try {
      const result = await fn();
      endTimer();
      return result;
    } catch (error) {
      endTimer();
      this.error(`Error in timed function ${label}`, error);
      throw error;
    }
  }

  /**
   * 记录内存使用情况
   * @param {string} label - 标签
   */
  memory(label = 'Memory Usage') {
    if (performance.memory) {
      const memory = {
        used: Math.round(performance.memory.usedJSHeapSize / 1024 / 1024),
        total: Math.round(performance.memory.totalJSHeapSize / 1024 / 1024),
        limit: Math.round(performance.memory.jsHeapSizeLimit / 1024 / 1024)
      };
      
      this.debug(label, {
        usedMB: memory.used,
        totalMB: memory.total,
        limitMB: memory.limit
      });
    } else {
      this.debug(`${label}: Memory API not available`);
    }
  }

  /**
   * 记录网络请求
   * @param {string} url - 请求URL
   * @param {string} method - 请求方法
   * @param {number} status - 响应状态码
   * @param {number} duration - 请求耗时
   */
  network(url, method = 'GET', status, duration) {
    const level = status >= 400 ? LogLevels.ERROR : LogLevels.DEBUG;
    this.log(level, `Network ${method} ${url}`, {
      status,
      duration: duration ? `${duration}ms` : undefined
    });
  }
}
