/**
 * 常量模块测试
 */

import { describe, it, expect } from 'vitest';
import {
  MessageTypes,
  DefaultSettings,
  RegexPatterns,
  CSSClasses
} from './constants.js';

describe('Constants', () => {
  describe('MessageTypes', () => {
    it('should have all required message types', () => {
      expect(MessageTypes.TOGGLE_EXTENSION).toBe('toggle_extension');
      expect(MessageTypes.GET_STATUS).toBe('get_status');
      expect(MessageTypes.ENABLE_PINYIN).toBe('enable_pinyin');
      expect(MessageTypes.DISABLE_PINYIN).toBe('disable_pinyin');
    });

    it('should have unique message type values', () => {
      const values = Object.values(MessageTypes);
      const uniqueValues = new Set(values);
      expect(uniqueValues.size).toBe(values.length);
    });
  });

  describe('DefaultSettings', () => {
    it('should have all required default settings', () => {
      expect(DefaultSettings).toHaveProperty('enabled');
      expect(DefaultSettings).toHaveProperty('mode');
      expect(DefaultSettings).toHaveProperty('displayStyle');
      expect(DefaultSettings).toHaveProperty('toneType');
      expect(DefaultSettings).toHaveProperty('scope');
    });

    it('should have correct default values', () => {
      expect(DefaultSettings.enabled).toBe(false);
      expect(DefaultSettings.mode).toBe('word');
      expect(DefaultSettings.displayStyle).toBe('ruby');
      expect(DefaultSettings.excludeDomains).toEqual([]);
    });

    it('should have valid data types', () => {
      expect(typeof DefaultSettings.enabled).toBe('boolean');
      expect(typeof DefaultSettings.mode).toBe('string');
      expect(typeof DefaultSettings.autoEnable).toBe('boolean');
      expect(Array.isArray(DefaultSettings.excludeDomains)).toBe(true);
      expect(typeof DefaultSettings.wordFrequencyThreshold).toBe('number');
    });
  });

  describe('RegexPatterns', () => {
    it('should match Chinese characters correctly', () => {
      const chineseText = '中文测试';
      const englishText = 'English test';
      const mixedText = 'Hello 世界';

      // 使用match方法来测试，避免全局标志的状态问题
      expect(chineseText.match(RegexPatterns.CHINESE_CHAR)).not.toBeNull();
      expect(englishText.match(RegexPatterns.CHINESE_CHAR)).toBeNull();
      expect(mixedText.match(RegexPatterns.CHINESE_CHAR)).not.toBeNull();
    });

    it('should match Chinese words correctly', () => {
      const chineseWord = '中国';
      const matches = chineseWord.match(RegexPatterns.CHINESE_WORD);
      expect(matches).not.toBeNull();
      expect(matches[0]).toBe('中国');
    });

    it('should validate domain names correctly', () => {
      const validDomains = ['example.com', 'sub.example.com', 'test-site.org'];
      const invalidDomains = ['', '.com', 'invalid..com', '-invalid.com'];

      validDomains.forEach(domain => {
        expect(RegexPatterns.DOMAIN.test(domain)).toBe(true);
      });

      invalidDomains.forEach(domain => {
        expect(RegexPatterns.DOMAIN.test(domain)).toBe(false);
      });
    });
  });

  describe('CSSClasses', () => {
    it('should have all required CSS class names', () => {
      expect(CSSClasses.PINYIN_CONTAINER).toBe('pinyin-container');
      expect(CSSClasses.RUBY_STYLE).toBe('pinyin-ruby');
      expect(CSSClasses.TOOLTIP_STYLE).toBe('pinyin-tooltip');
      expect(CSSClasses.BRACKET_STYLE).toBe('pinyin-bracket');
    });

    it('should have consistent naming convention', () => {
      const classNames = Object.values(CSSClasses);
      classNames.forEach(className => {
        expect(className).toMatch(/^pinyin-/);
        expect(className).not.toContain(' ');
        expect(className).not.toContain('_');
      });
    });
  });
});
