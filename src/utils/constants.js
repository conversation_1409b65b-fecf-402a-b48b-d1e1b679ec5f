/**
 * Chrome拼音标注插件 - 常量定义
 * 定义插件中使用的所有常量
 */

// 消息类型
export const MessageTypes = {
  // Popup → Service Worker
  TOGGLE_EXTENSION: 'toggle_extension',
  GET_STATUS: 'get_status',
  CHANGE_MODE: 'change_mode',

  // Service Worker → Content Script
  ENABLE_PINYIN: 'enable_pinyin',
  DISABLE_PINYIN: 'disable_pinyin',
  UPDATE_SETTINGS: 'update_settings',

  // Content Script → Service Worker
  STATUS_UPDATE: 'status_update',
  ERROR_REPORT: 'error_report',

  // Options → Service Worker
  SAVE_SETTINGS: 'save_settings',
  RESET_SETTINGS: 'reset_settings',

  // 通用消息
  PING: 'ping',
  PONG: 'pong'
};

// 错误类型
export const ErrorTypes = {
  CONVERSION_ERROR: 'conversion_error',
  DOM_ERROR: 'dom_error',
  STORAGE_ERROR: 'storage_error',
  NETWORK_ERROR: 'network_error',
  PERMISSION_ERROR: 'permission_error',
  UNKNOWN_ERROR: 'unknown_error'
};

// 日志级别
export const LogLevels = {
  DEBUG: 'debug',
  INFO: 'info',
  WARN: 'warn',
  ERROR: 'error'
};

// 显示样式
export const DisplayStyles = {
  RUBY: 'ruby',
  TOOLTIP: 'tooltip',
  BRACKET: 'bracket'
};

// 标注模式
export const AnnotationModes = {
  CHAR: 'char',
  WORD: 'word'
};

// 拼音格式
export const PinyinFormats = {
  SYMBOL: 'symbol',    // zhōng
  NUMBER: 'number',    // zhong1
  NONE: 'none'         // zhong
};

// 标注范围
export const AnnotationScopes = {
  ALL: 'all',           // 全页面
  SMART: 'smart',       // 智能识别内容区域
  SELECTION: 'selection', // 选中区域
  TITLE: 'title',       // 标题区域
  CONTENT: 'content'    // 内容区域
};

// 字体大小
export const FontSizes = {
  SMALL: 'small',
  MEDIUM: 'medium',
  LARGE: 'large'
};

// 颜色主题
export const ColorThemes = {
  BLUE: 'blue',
  GREEN: 'green',
  RED: 'red',
  GRAY: 'gray'
};

// 默认设置
export const DefaultSettings = {
  enabled: false,
  mode: AnnotationModes.WORD,
  displayStyle: DisplayStyles.RUBY,
  toneType: PinyinFormats.SYMBOL,
  scope: AnnotationScopes.SMART,
  rareWordOnly: false,
  autoEnable: false,
  excludeDomains: [],
  fontSize: FontSizes.MEDIUM,
  colorTheme: ColorThemes.BLUE,
  wordFrequencyThreshold: 5,
  enableHoverTooltip: true,  // 启用悬浮拼音提示
  polyphone: true,           // 启用多音字处理
  showTooltipInfo: true      // 在提示框中显示额外信息
};

// 性能配置
export const PerformanceConfig = {
  MAX_PROCESSING_TIME: 5000,      // 最大处理时间 (ms)
  BATCH_SIZE: 100,                // 批处理大小
  DEBOUNCE_DELAY: 300,            // 防抖延迟 (ms)
  CACHE_EXPIRE_TIME: 3600000,     // 缓存过期时间 (1小时)
  MAX_CACHE_SIZE: 10000           // 最大缓存条目数
};

// DOM选择器
export const DOMSelectors = {
  // 需要处理的文本节点容器
  TEXT_CONTAINERS: [
    'p', 'div', 'span', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6',
    'li', 'td', 'th', 'blockquote', 'article', 'section',
    'header', 'footer', 'main', 'aside', 'nav'
  ],

  // 需要排除的元素
  EXCLUDED_ELEMENTS: [
    'script', 'style', 'noscript', 'iframe', 'object', 'embed',
    'canvas', 'svg', 'math', 'code', 'pre', 'textarea', 'input',
    'select', 'button'
  ],

  // 已处理的标记
  PROCESSED_CLASS: 'pinyin-processed',
  ANNOTATION_CLASS: 'pinyin-annotation',
  TOOLTIP_CLASS: 'pinyin-tooltip'
};

// 正则表达式
export const RegexPatterns = {
  // 中文字符 (CJK统一汉字基本区块)
  CHINESE_CHAR: /[\u4e00-\u9fff]/g,

  // 中文词汇 (连续的中文字符)
  CHINESE_WORD: /[\u4e00-\u9fff]+/g,

  // 标点符号
  PUNCTUATION: /[\u3000-\u303f\uff00-\uffef]/g,

  // 空白字符
  WHITESPACE: /\s+/g,

  // 域名验证
  DOMAIN: /^[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?(\.[a-zA-Z0-9]([a-zA-Z0-9-]{0,61}[a-zA-Z0-9])?)*$/
};

// CSS类名
export const CSSClasses = {
  // 基础类名
  PINYIN_CONTAINER: 'pinyin-container',
  PINYIN_TEXT: 'pinyin-text',
  PINYIN_ANNOTATION: 'pinyin-annotation',

  // 样式类名
  RUBY_STYLE: 'pinyin-ruby',
  TOOLTIP_STYLE: 'pinyin-tooltip',
  BRACKET_STYLE: 'pinyin-bracket',

  // 状态类名
  PROCESSING: 'pinyin-processing',
  PROCESSED: 'pinyin-processed',
  ERROR: 'pinyin-error',

  // 主题类名
  THEME_BLUE: 'pinyin-theme-blue',
  THEME_GREEN: 'pinyin-theme-green',
  THEME_RED: 'pinyin-theme-red',
  THEME_GRAY: 'pinyin-theme-gray',

  // 大小类名
  SIZE_SMALL: 'pinyin-size-small',
  SIZE_MEDIUM: 'pinyin-size-medium',
  SIZE_LARGE: 'pinyin-size-large'
};

// 存储键名
export const StorageKeys = {
  SETTINGS: 'settings',
  CACHE: 'cache',
  STATS: 'stats',
  USER_DATA: 'userData'
};

// 统计事件
export const StatEvents = {
  EXTENSION_ENABLED: 'extension_enabled',
  EXTENSION_DISABLED: 'extension_disabled',
  MODE_CHANGED: 'mode_changed',
  STYLE_CHANGED: 'style_changed',
  TEXT_PROCESSED: 'text_processed',
  ERROR_OCCURRED: 'error_occurred'
};

// 多音字常见词汇映射 (用于上下文分析)
export const PolyphoneContext = {
  '行': {
    '银行': 'háng',
    '行走': 'xíng',
    '行业': 'háng',
    '步行': 'xíng',
    '行为': 'xíng',
    '行程': 'xíng',
    '行列': 'háng',
    '行政': 'xíng'
  },
  '中': {
    '中国': 'zhōng',
    '中奖': 'zhòng',
    '中心': 'zhōng',
    '击中': 'zhòng',
    '中间': 'zhōng',
    '中毒': 'zhòng',
    '中央': 'zhōng',
    '中弹': 'zhòng'
  },
  '长': {
    '长度': 'cháng',
    '长大': 'zhǎng',
    '长江': 'cháng',
    '成长': 'zhǎng',
    '长短': 'cháng',
    '长辈': 'zhǎng',
    '长城': 'cháng',
    '长官': 'zhǎng'
  },
  '重': {
    '重要': 'zhòng',
    '重复': 'chóng',
    '重量': 'zhòng',
    '重新': 'chóng',
    '重点': 'zhòng',
    '重叠': 'chóng',
    '重视': 'zhòng',
    '重来': 'chóng'
  },
  '还': {
    '还是': 'hái',
    '还有': 'hái',
    '还钱': 'huán',
    '还书': 'huán',
    '还给': 'huán',
    '还债': 'huán',
    '还原': 'huán',
    '还好': 'hái'
  },
  '为': {
    '为了': 'wèi',
    '为什么': 'wèi',
    '为人': 'wéi',
    '为难': 'wéi',
    '为止': 'wéi',
    '为主': 'wéi',
    '为准': 'wéi',
    '为期': 'wéi'
  },
  '得': {
    '得到': 'dé',
    '得意': 'dé',
    '跑得快': 'de',
    '说得对': 'de',
    '得了': 'děi',
    '得去': 'děi',
    '得病': 'dé',
    '心得': 'dé'
  },
  '都': {
    '都是': 'dōu',
    '都有': 'dōu',
    '首都': 'dū',
    '都市': 'dū',
    '都城': 'dū',
    '古都': 'dū',
    '都督': 'dū',
    '都会': 'dū'
  }
};

// 常用字频率数据 (简化版，实际应用中应使用完整的字频数据库)
export const CommonCharacters = new Set([
  '的', '一', '是', '在', '不', '了', '有', '和', '人', '这',
  '中', '大', '为', '上', '个', '国', '我', '以', '要', '他',
  '时', '来', '用', '们', '生', '到', '作', '地', '于', '出',
  '就', '分', '对', '成', '会', '可', '主', '发', '年', '动',
  '同', '工', '也', '能', '下', '过', '子', '说', '产', '种',
  '面', '而', '方', '后', '多', '定', '行', '学', '法', '所'
]);

// 网站特殊处理规则
export const SiteRules = {
  // 需要特殊处理的网站
  'weibo.com': {
    excludeSelectors: ['.toolbar', '.comment-list'],
    delayProcessing: 1000
  },
  'zhihu.com': {
    excludeSelectors: ['.Topstory-container'],
    observeChanges: true
  },
  'baidu.com': {
    excludeSelectors: ['#result', '.result'],
    processOnScroll: true
  }
};

// 版本信息
export const Version = {
  CURRENT: '1.0.0',
  MIN_CHROME_VERSION: 88,
  MANIFEST_VERSION: 3
};
