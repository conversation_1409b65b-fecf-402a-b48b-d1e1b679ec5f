/**
 * Chrome拼音标注插件 - 存储管理器
 * 负责处理Chrome Storage API的封装和数据管理
 */

import { DefaultSettings, StorageKeys, ErrorTypes } from './constants.js';
import { Logger } from './logger.js';

export class StorageManager {
  constructor() {
    this.logger = new Logger('Storage');
  }

  /**
   * 获取用户设置
   * @returns {Promise<Object>} 用户设置对象
   */
  async getSettings() {
    try {
      const result = await chrome.storage.sync.get(StorageKeys.SETTINGS);
      const settings = result[StorageKeys.SETTINGS];

      if (!settings) {
        this.logger.info('No settings found, returning defaults');
        return { ...DefaultSettings };
      }

      // 合并默认设置，确保新增的设置项有默认值
      const mergedSettings = { ...DefaultSettings, ...settings };

      this.logger.debug('Settings retrieved', mergedSettings);
      return mergedSettings;
    } catch (error) {
      this.logger.error('Failed to get settings', error);
      throw new Error(`${ErrorTypes.STORAGE_ERROR}: Failed to get settings`);
    }
  }

  /**
   * 保存用户设置
   * @param {Object} settings - 要保存的设置对象
   * @returns {Promise<void>}
   */
  async saveSettings(settings) {
    try {
      // 验证设置对象
      const validatedSettings = this.validateSettings(settings);

      await chrome.storage.sync.set({
        [StorageKeys.SETTINGS]: validatedSettings
      });

      this.logger.info('Settings saved successfully', validatedSettings);
    } catch (error) {
      this.logger.error('Failed to save settings', error);
      throw new Error(`${ErrorTypes.STORAGE_ERROR}: Failed to save settings`);
    }
  }

  /**
   * 重置设置为默认值
   * @returns {Promise<void>}
   */
  async resetSettings() {
    try {
      await this.saveSettings(DefaultSettings);
      this.logger.info('Settings reset to defaults');
    } catch (error) {
      this.logger.error('Failed to reset settings', error);
      throw error;
    }
  }

  /**
   * 获取缓存数据
   * @param {string} key - 缓存键名
   * @returns {Promise<any>} 缓存数据
   */
  async getCache(key) {
    try {
      const cacheKey = `${StorageKeys.CACHE}.${key}`;
      const result = await chrome.storage.local.get(cacheKey);
      const cacheData = result[cacheKey];

      if (!cacheData) {
        return null;
      }

      // 检查缓存是否过期
      if (cacheData.expireTime && Date.now() > cacheData.expireTime) {
        await this.removeCache(key);
        return null;
      }

      this.logger.debug(`Cache hit for key: ${key}`);
      return cacheData.data;
    } catch (error) {
      this.logger.error(`Failed to get cache for key: ${key}`, error);
      return null;
    }
  }

  /**
   * 设置缓存数据
   * @param {string} key - 缓存键名
   * @param {any} data - 要缓存的数据
   * @param {number} ttl - 生存时间(毫秒)，默认1小时
   * @returns {Promise<void>}
   */
  async setCache(key, data, ttl = 3600000) {
    try {
      const cacheKey = `${StorageKeys.CACHE}.${key}`;
      const cacheData = {
        data,
        createTime: Date.now(),
        expireTime: ttl > 0 ? Date.now() + ttl : null
      };

      await chrome.storage.local.set({
        [cacheKey]: cacheData
      });

      this.logger.debug(`Cache set for key: ${key}`);
    } catch (error) {
      this.logger.error(`Failed to set cache for key: ${key}`, error);
    }
  }

  /**
   * 删除缓存数据
   * @param {string} key - 缓存键名
   * @returns {Promise<void>}
   */
  async removeCache(key) {
    try {
      const cacheKey = `${StorageKeys.CACHE}.${key}`;
      await chrome.storage.local.remove(cacheKey);
      this.logger.debug(`Cache removed for key: ${key}`);
    } catch (error) {
      this.logger.error(`Failed to remove cache for key: ${key}`, error);
    }
  }

  /**
   * 清理所有缓存
   * @returns {Promise<void>}
   */
  async clearCache() {
    try {
      const result = await chrome.storage.local.get(null);
      const cacheKeys = Object.keys(result).filter(key =>
        key.startsWith(StorageKeys.CACHE)
      );

      if (cacheKeys.length > 0) {
        await chrome.storage.local.remove(cacheKeys);
        this.logger.info(`Cleared ${cacheKeys.length} cache entries`);
      }
    } catch (error) {
      this.logger.error('Failed to clear cache', error);
      throw new Error(`${ErrorTypes.STORAGE_ERROR}: Failed to clear cache`);
    }
  }

  /**
   * 获取缓存大小信息
   * @returns {Promise<Object>} 缓存大小信息
   */
  async getCacheSize() {
    try {
      const result = await chrome.storage.local.get(null);
      const cacheEntries = Object.entries(result).filter(([key]) =>
        key.startsWith(StorageKeys.CACHE)
      );

      const totalSize = JSON.stringify(cacheEntries).length;
      const entryCount = cacheEntries.length;

      return {
        totalSize,
        entryCount,
        formattedSize: this.formatBytes(totalSize)
      };
    } catch (error) {
      this.logger.error('Failed to get cache size', error);
      return { totalSize: 0, entryCount: 0, formattedSize: '0 B' };
    }
  }

  /**
   * 获取统计数据
   * @returns {Promise<Object>} 统计数据
   */
  async getStats() {
    try {
      const result = await chrome.storage.local.get(StorageKeys.STATS);
      const stats = result[StorageKeys.STATS];

      if (!stats) {
        return await this.initStats();
      }

      return stats;
    } catch (error) {
      this.logger.error('Failed to get stats', error);
      return await this.initStats();
    }
  }

  /**
   * 初始化统计数据
   * @returns {Promise<Object>} 初始化的统计数据
   */
  async initStats() {
    const defaultStats = {
      totalUsage: 0,
      lastUsed: null,
      installTime: Date.now(),
      charactersProcessed: 0,
      wordsProcessed: 0,
      errorsOccurred: 0,
      favoriteWords: []
    };

    try {
      await chrome.storage.local.set({
        [StorageKeys.STATS]: defaultStats
      });

      this.logger.info('Stats initialized');
      return defaultStats;
    } catch (error) {
      this.logger.error('Failed to initialize stats', error);
      return defaultStats;
    }
  }

  /**
   * 更新统计数据
   * @param {Object} updates - 要更新的统计数据
   * @returns {Promise<void>}
   */
  async updateStats(updates) {
    try {
      const currentStats = await this.getStats();
      const newStats = { ...currentStats, ...updates };

      await chrome.storage.local.set({
        [StorageKeys.STATS]: newStats
      });

      this.logger.debug('Stats updated', updates);
    } catch (error) {
      this.logger.error('Failed to update stats', error);
    }
  }

  /**
   * 增加使用次数
   * @returns {Promise<void>}
   */
  async incrementUsage() {
    try {
      const stats = await this.getStats();
      await this.updateStats({
        totalUsage: stats.totalUsage + 1,
        lastUsed: Date.now()
      });
    } catch (error) {
      this.logger.error('Failed to increment usage', error);
    }
  }

  /**
   * 导出所有数据
   * @returns {Promise<Object>} 导出的数据
   */
  async exportData() {
    try {
      const [syncData, localData] = await Promise.all([
        chrome.storage.sync.get(null),
        chrome.storage.local.get(null)
      ]);

      const exportData = {
        version: '1.0.0',
        exportTime: Date.now(),
        settings: syncData[StorageKeys.SETTINGS] || DefaultSettings,
        stats: localData[StorageKeys.STATS] || {},
        // 不导出缓存数据，因为它们可以重新生成
      };

      this.logger.info('Data exported successfully');
      return exportData;
    } catch (error) {
      this.logger.error('Failed to export data', error);
      throw new Error(`${ErrorTypes.STORAGE_ERROR}: Failed to export data`);
    }
  }

  /**
   * 导入数据
   * @param {Object} data - 要导入的数据
   * @returns {Promise<void>}
   */
  async importData(data) {
    try {
      if (!data || typeof data !== 'object') {
        throw new Error('Invalid import data format');
      }

      // 验证数据版本
      if (data.version && data.version !== '1.0.0') {
        this.logger.warn('Import data version mismatch', {
          expected: '1.0.0',
          actual: data.version
        });
      }

      // 导入设置
      if (data.settings) {
        await this.saveSettings(data.settings);
      }

      // 导入统计数据
      if (data.stats) {
        await chrome.storage.local.set({
          [StorageKeys.STATS]: data.stats
        });
      }

      this.logger.info('Data imported successfully');
    } catch (error) {
      this.logger.error('Failed to import data', error);
      throw new Error(`${ErrorTypes.STORAGE_ERROR}: Failed to import data`);
    }
  }

  /**
   * 验证设置对象
   * @param {Object} settings - 要验证的设置对象
   * @returns {Object} 验证后的设置对象
   */
  validateSettings(settings) {
    const validated = { ...DefaultSettings };

    // 验证每个设置项
    Object.keys(DefaultSettings).forEach(key => {
      if (Object.prototype.hasOwnProperty.call(settings, key)) {
        const value = settings[key];
        const defaultValue = DefaultSettings[key];

        // 类型检查
        if (typeof value === typeof defaultValue) {
          if (Array.isArray(defaultValue)) {
            validated[key] = Array.isArray(value) ? value : defaultValue;
          } else {
            validated[key] = value;
          }
        } else {
          this.logger.warn(`Invalid type for setting ${key}, using default`);
          validated[key] = defaultValue;
        }
      }
    });

    return validated;
  }

  /**
   * 格式化字节大小
   * @param {number} bytes - 字节数
   * @returns {string} 格式化后的大小字符串
   */
  formatBytes(bytes) {
    if (bytes === 0) return '0 B';

    const k = 1024;
    const sizes = ['B', 'KB', 'MB', 'GB'];
    const i = Math.floor(Math.log(bytes) / Math.log(k));

    return parseFloat((bytes / Math.pow(k, i)).toFixed(2)) + ' ' + sizes[i];
  }

  /**
   * 监听存储变化
   * @param {Function} callback - 变化回调函数
   */
  onStorageChanged(callback) {
    chrome.storage.onChanged.addListener((changes, namespace) => {
      this.logger.debug('Storage changed', { changes, namespace });
      callback(changes, namespace);
    });
  }
}
