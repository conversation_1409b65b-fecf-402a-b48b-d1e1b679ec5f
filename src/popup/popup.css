/**
 * Chrome拼音标注插件 - Popup样式
 * 遵循Material Design和Chrome扩展设计规范
 */

/* CSS变量定义 */
:root {
  /* 主色调 */
  --primary-color: #1a73e8;
  --primary-hover: #1557b0;
  --primary-active: #0d47a1;
  
  /* 辅助色 */
  --secondary-color: #5f6368;
  --success-color: #137333;
  --warning-color: #ea8600;
  --error-color: #d93025;
  
  /* 背景色 */
  --bg-primary: #ffffff;
  --bg-secondary: #f8f9fa;
  --bg-hover: #f1f3f4;
  
  /* 边框色 */
  --border-color: #dadce0;
  --border-focus: #1a73e8;
  
  /* 文字色 */
  --text-primary: #202124;
  --text-secondary: #5f6368;
  --text-disabled: #9aa0a6;
  
  /* 间距 */
  --spacing-xs: 4px;
  --spacing-sm: 8px;
  --spacing-md: 16px;
  --spacing-lg: 24px;
  
  /* 圆角和阴影 */
  --border-radius-sm: 4px;
  --border-radius-md: 8px;
  --shadow-sm: 0 1px 3px rgba(0,0,0,0.1);
  --shadow-md: 0 2px 8px rgba(0,0,0,0.15);
}

/* 基础重置 */
* {
  box-sizing: border-box;
  margin: 0;
  padding: 0;
}

body {
  font-family: 'Roboto', 'Noto Sans SC', sans-serif;
  font-size: 14px;
  line-height: 1.4;
  color: var(--text-primary);
  background: var(--bg-primary);
  width: 320px;
  min-height: 200px;
}

/* 主容器 */
.popup-container {
  display: flex;
  flex-direction: column;
  min-height: 200px;
}

/* 标题栏 */
.popup-header {
  padding: var(--spacing-md);
  border-bottom: 1px solid var(--border-color);
  background: var(--bg-secondary);
}

.popup-title {
  display: flex;
  align-items: center;
  font-size: 16px;
  font-weight: 500;
  color: var(--text-primary);
}

.title-icon {
  width: 24px;
  height: 24px;
  margin-right: var(--spacing-sm);
}

/* 主要内容 */
.popup-main {
  flex: 1;
  padding: var(--spacing-md);
}

/* 控制区域 */
.control-section {
  margin-bottom: var(--spacing-lg);
}

.toggle-container {
  display: flex;
  align-items: center;
  justify-content: space-between;
  margin-bottom: var(--spacing-sm);
}

.toggle-label {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  cursor: pointer;
  user-select: none;
}

.toggle-text {
  font-size: 14px;
  font-weight: 500;
  color: var(--text-primary);
}

/* 开关样式 */
.toggle-switch {
  position: relative;
  width: 44px;
  height: 24px;
  background: var(--border-color);
  border-radius: 12px;
  cursor: pointer;
  transition: background 0.2s ease;
  border: none;
  outline: none;
}

.toggle-switch[aria-checked="true"] {
  background: var(--primary-color);
}

.toggle-switch:focus {
  box-shadow: 0 0 0 2px rgba(26, 115, 232, 0.2);
}

.toggle-slider {
  position: absolute;
  width: 20px;
  height: 20px;
  background: white;
  border-radius: 50%;
  top: 2px;
  left: 2px;
  transition: transform 0.2s ease;
  box-shadow: var(--shadow-sm);
}

.toggle-switch[aria-checked="true"] .toggle-slider {
  transform: translateX(20px);
}

/* 状态指示器 */
.status-indicator {
  display: flex;
  align-items: center;
  margin-top: var(--spacing-sm);
}

.status-text {
  font-size: 12px;
  color: var(--text-secondary);
}

/* 区域标题 */
.section-title {
  font-size: 14px;
  font-weight: 500;
  color: var(--text-primary);
  margin-bottom: var(--spacing-sm);
}

/* 模式选择区域 */
.mode-section {
  margin-bottom: var(--spacing-lg);
}

.radio-group {
  display: flex;
  gap: var(--spacing-md);
}

.radio-label {
  display: flex;
  align-items: center;
  cursor: pointer;
  user-select: none;
}

.radio-input {
  display: none;
}

.radio-custom {
  width: 16px;
  height: 16px;
  border: 2px solid var(--border-color);
  border-radius: 50%;
  margin-right: var(--spacing-xs);
  position: relative;
  transition: border-color 0.2s ease;
}

.radio-input:checked + .radio-custom {
  border-color: var(--primary-color);
}

.radio-input:checked + .radio-custom::after {
  content: '';
  position: absolute;
  width: 8px;
  height: 8px;
  background: var(--primary-color);
  border-radius: 50%;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.radio-text {
  font-size: 14px;
  color: var(--text-primary);
}

/* 样式选择区域 */
.style-section {
  margin-bottom: var(--spacing-lg);
}

.select-container {
  position: relative;
}

.select-dropdown {
  width: 100%;
  padding: var(--spacing-sm) var(--spacing-md);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-sm);
  background: white;
  font-size: 14px;
  color: var(--text-primary);
  cursor: pointer;
  appearance: none;
  background-image: url("data:image/svg+xml,%3csvg xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 20 20'%3e%3cpath stroke='%236b7280' stroke-linecap='round' stroke-linejoin='round' stroke-width='1.5' d='m6 8 4 4 4-4'/%3e%3c/svg%3e");
  background-position: right 8px center;
  background-repeat: no-repeat;
  background-size: 16px;
  padding-right: 32px;
}

.select-dropdown:focus {
  outline: none;
  border-color: var(--border-focus);
  box-shadow: 0 0 0 2px rgba(26, 115, 232, 0.2);
}

/* 快速设置 */
.quick-settings {
  margin-bottom: var(--spacing-lg);
}

.checkbox-container {
  display: flex;
  align-items: center;
}

.checkbox-label {
  display: flex;
  align-items: center;
  cursor: pointer;
  user-select: none;
}

.checkbox-input {
  display: none;
}

.checkbox-custom {
  width: 16px;
  height: 16px;
  border: 2px solid var(--border-color);
  border-radius: 2px;
  margin-right: var(--spacing-sm);
  position: relative;
  transition: all 0.2s ease;
}

.checkbox-input:checked + .checkbox-custom {
  background: var(--primary-color);
  border-color: var(--primary-color);
}

.checkbox-input:checked + .checkbox-custom::after {
  content: '✓';
  position: absolute;
  color: white;
  font-size: 12px;
  top: 50%;
  left: 50%;
  transform: translate(-50%, -50%);
}

.checkbox-text {
  font-size: 14px;
  color: var(--text-primary);
}

/* 底部操作区域 */
.popup-footer {
  padding: var(--spacing-md);
  border-top: 1px solid var(--border-color);
  background: var(--bg-secondary);
}

.button-group {
  display: flex;
  gap: var(--spacing-sm);
}

/* 按钮样式 */
.btn {
  display: inline-flex;
  align-items: center;
  justify-content: center;
  padding: 6px 12px;
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius-sm);
  font-size: 14px;
  font-weight: 500;
  cursor: pointer;
  transition: all 0.2s ease;
  text-decoration: none;
  background: white;
  color: var(--text-primary);
  flex: 1;
}

.btn:hover {
  background: var(--bg-hover);
}

.btn:focus {
  outline: none;
  box-shadow: 0 0 0 2px rgba(26, 115, 232, 0.2);
}

.btn-primary {
  background: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

.btn-primary:hover {
  background: var(--primary-hover);
  border-color: var(--primary-hover);
}

.btn-secondary {
  background: white;
  color: var(--text-primary);
  border-color: var(--border-color);
}

.btn-icon {
  margin-right: var(--spacing-xs);
  font-size: 12px;
}

/* 加载指示器 */
.loading-overlay {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(255, 255, 255, 0.9);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  z-index: 1000;
}

.loading-spinner {
  width: 24px;
  height: 24px;
  border: 2px solid var(--border-color);
  border-top: 2px solid var(--primary-color);
  border-radius: 50%;
  animation: spin 1s linear infinite;
  margin-bottom: var(--spacing-sm);
}

@keyframes spin {
  0% { transform: rotate(0deg); }
  100% { transform: rotate(360deg); }
}

.loading-text {
  font-size: 14px;
  color: var(--text-secondary);
}

/* 错误提示 */
.error-toast {
  position: absolute;
  bottom: var(--spacing-md);
  left: var(--spacing-md);
  right: var(--spacing-md);
  background: var(--error-color);
  color: white;
  padding: var(--spacing-sm) var(--spacing-md);
  border-radius: var(--border-radius-sm);
  box-shadow: var(--shadow-md);
  z-index: 1001;
  animation: slideUp 0.3s ease-out;
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.error-content {
  display: flex;
  align-items: center;
}

.error-icon {
  margin-right: var(--spacing-sm);
  font-size: 16px;
}

.error-message {
  font-size: 14px;
  flex: 1;
}

/* 响应式适配 */
@media (max-width: 320px) {
  body {
    width: 280px;
  }
  
  .popup-container {
    padding: var(--spacing-sm);
  }
  
  .btn {
    padding: 4px 8px;
    font-size: 13px;
  }
}

/* 高对比度模式适配 */
@media (prefers-contrast: high) {
  :root {
    --border-color: #000000;
    --text-secondary: #000000;
  }
}

/* 深色模式适配 */
@media (prefers-color-scheme: dark) {
  :root {
    --bg-primary: #202124;
    --bg-secondary: #303134;
    --bg-hover: #3c4043;
    --text-primary: #e8eaed;
    --text-secondary: #9aa0a6;
    --border-color: #5f6368;
  }
}
