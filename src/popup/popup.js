/**
 * Chrome拼音标注插件 - Popup界面脚本
 * 处理弹窗界面的交互逻辑和与后台脚本的通信
 */

import { MessageTypes, AnnotationModes, DisplayStyles } from '../utils/constants.js';

class PopupController {
  constructor() {
    this.currentSettings = null;
    this.currentTab = null;
    this.isLoading = false;
    
    this.init();
  }

  /**
   * 初始化弹窗控制器
   */
  async init() {
    try {
      // 获取当前标签页信息
      await this.getCurrentTab();
      
      // 绑定事件监听器
      this.bindEventListeners();
      
      // 加载当前状态
      await this.loadCurrentStatus();
      
      console.log('Popup controller initialized');
    } catch (error) {
      console.error('Failed to initialize popup controller:', error);
      this.showError('初始化失败，请刷新页面重试');
    }
  }

  /**
   * 获取当前活跃标签页
   */
  async getCurrentTab() {
    const [tab] = await chrome.tabs.query({ active: true, currentWindow: true });
    this.currentTab = tab;
  }

  /**
   * 绑定事件监听器
   */
  bindEventListeners() {
    // 主开关
    const mainToggle = document.getElementById('main-toggle');
    if (mainToggle) {
      mainToggle.addEventListener('click', this.handleToggleExtension.bind(this));
      mainToggle.addEventListener('keydown', (e) => {
        if (e.key === 'Enter' || e.key === ' ') {
          e.preventDefault();
          this.handleToggleExtension();
        }
      });
    }

    // 标注模式选择
    const modeRadios = document.querySelectorAll('input[name="mode"]');
    modeRadios.forEach(radio => {
      radio.addEventListener('change', this.handleModeChange.bind(this));
    });

    // 显示样式选择
    const displayStyleSelect = document.getElementById('display-style');
    if (displayStyleSelect) {
      displayStyleSelect.addEventListener('change', this.handleStyleChange.bind(this));
    }

    // 生僻字选项
    const rareWordsCheckbox = document.getElementById('rare-words-only');
    if (rareWordsCheckbox) {
      rareWordsCheckbox.addEventListener('change', this.handleRareWordsChange.bind(this));
    }

    // 更多设置按钮
    const moreSettingsBtn = document.getElementById('more-settings');
    if (moreSettingsBtn) {
      moreSettingsBtn.addEventListener('click', this.handleMoreSettings.bind(this));
    }

    // 帮助按钮
    const helpBtn = document.getElementById('help-btn');
    if (helpBtn) {
      helpBtn.addEventListener('click', this.handleHelp.bind(this));
    }
  }

  /**
   * 加载当前状态
   */
  async loadCurrentStatus() {
    try {
      this.setLoading(true);
      
      const response = await this.sendMessage({
        type: MessageTypes.GET_STATUS
      });

      if (response.success) {
        this.currentSettings = response.data;
        this.updateUI();
      } else {
        throw new Error(response.error || '获取状态失败');
      }
    } catch (error) {
      console.error('Failed to load current status:', error);
      this.showError('加载状态失败');
    } finally {
      this.setLoading(false);
    }
  }

  /**
   * 更新UI界面
   */
  updateUI() {
    if (!this.currentSettings) return;

    // 更新主开关状态
    this.updateToggleState(this.currentSettings.tabEnabled || false);

    // 更新标注模式
    this.updateModeSelection(this.currentSettings.mode || AnnotationModes.WORD);

    // 更新显示样式
    this.updateStyleSelection(this.currentSettings.displayStyle || DisplayStyles.RUBY);

    // 更新生僻字选项
    this.updateRareWordsOption(this.currentSettings.rareWordOnly || false);

    // 更新状态文本
    this.updateStatusText(this.currentSettings.tabEnabled || false);
  }

  /**
   * 更新开关状态
   */
  updateToggleState(enabled) {
    const toggle = document.getElementById('main-toggle');
    if (toggle) {
      toggle.setAttribute('aria-checked', enabled.toString());
    }
  }

  /**
   * 更新模式选择
   */
  updateModeSelection(mode) {
    const modeRadio = document.querySelector(`input[name="mode"][value="${mode}"]`);
    if (modeRadio) {
      modeRadio.checked = true;
    }
  }

  /**
   * 更新样式选择
   */
  updateStyleSelection(style) {
    const styleSelect = document.getElementById('display-style');
    if (styleSelect) {
      styleSelect.value = style;
    }
  }

  /**
   * 更新生僻字选项
   */
  updateRareWordsOption(enabled) {
    const checkbox = document.getElementById('rare-words-only');
    if (checkbox) {
      checkbox.checked = enabled;
    }
  }

  /**
   * 更新状态文本
   */
  updateStatusText(enabled) {
    const statusText = document.getElementById('status-text');
    if (statusText) {
      statusText.textContent = enabled ? '已启用' : '已禁用';
      statusText.style.color = enabled ? 'var(--success-color)' : 'var(--text-secondary)';
    }
  }

  /**
   * 处理扩展开关切换
   */
  async handleToggleExtension() {
    if (this.isLoading) return;

    try {
      this.setLoading(true);

      const response = await this.sendMessage({
        type: MessageTypes.TOGGLE_EXTENSION
      });

      if (response.success) {
        const enabled = response.data.enabled;
        this.updateToggleState(enabled);
        this.updateStatusText(enabled);
        
        // 更新当前设置
        if (this.currentSettings) {
          this.currentSettings.tabEnabled = enabled;
        }
      } else {
        throw new Error(response.error || '切换失败');
      }
    } catch (error) {
      console.error('Failed to toggle extension:', error);
      this.showError('操作失败，请重试');
    } finally {
      this.setLoading(false);
    }
  }

  /**
   * 处理模式变更
   */
  async handleModeChange(event) {
    const mode = event.target.value;
    
    try {
      const response = await this.sendMessage({
        type: MessageTypes.CHANGE_MODE,
        data: { mode }
      });

      if (response.success) {
        this.currentSettings = response.data;
        console.log(`Mode changed to: ${mode}`);
      } else {
        throw new Error(response.error || '模式切换失败');
      }
    } catch (error) {
      console.error('Failed to change mode:', error);
      this.showError('模式切换失败');
      // 恢复之前的选择
      this.updateModeSelection(this.currentSettings?.mode || AnnotationModes.WORD);
    }
  }

  /**
   * 处理样式变更
   */
  async handleStyleChange(event) {
    const displayStyle = event.target.value;
    
    try {
      const newSettings = {
        ...this.currentSettings,
        displayStyle
      };

      const response = await this.sendMessage({
        type: MessageTypes.UPDATE_SETTINGS,
        data: newSettings
      });

      if (response.success) {
        this.currentSettings.displayStyle = displayStyle;
        console.log(`Display style changed to: ${displayStyle}`);
      } else {
        throw new Error(response.error || '样式切换失败');
      }
    } catch (error) {
      console.error('Failed to change display style:', error);
      this.showError('样式切换失败');
      // 恢复之前的选择
      this.updateStyleSelection(this.currentSettings?.displayStyle || DisplayStyles.RUBY);
    }
  }

  /**
   * 处理生僻字选项变更
   */
  async handleRareWordsChange(event) {
    const rareWordOnly = event.target.checked;
    
    try {
      const newSettings = {
        ...this.currentSettings,
        rareWordOnly
      };

      const response = await this.sendMessage({
        type: MessageTypes.UPDATE_SETTINGS,
        data: newSettings
      });

      if (response.success) {
        this.currentSettings.rareWordOnly = rareWordOnly;
        console.log(`Rare words only: ${rareWordOnly}`);
      } else {
        throw new Error(response.error || '设置更新失败');
      }
    } catch (error) {
      console.error('Failed to update rare words setting:', error);
      this.showError('设置更新失败');
      // 恢复之前的状态
      this.updateRareWordsOption(this.currentSettings?.rareWordOnly || false);
    }
  }

  /**
   * 处理更多设置按钮点击
   */
  handleMoreSettings() {
    chrome.runtime.openOptionsPage();
  }

  /**
   * 处理帮助按钮点击
   */
  handleHelp() {
    const helpUrl = chrome.runtime.getURL('help.html');
    chrome.tabs.create({ url: helpUrl });
  }

  /**
   * 发送消息到后台脚本
   */
  async sendMessage(message) {
    return new Promise((resolve) => {
      chrome.runtime.sendMessage(message, (response) => {
        if (chrome.runtime.lastError) {
          resolve({
            success: false,
            error: chrome.runtime.lastError.message
          });
        } else {
          resolve(response || { success: false, error: 'No response' });
        }
      });
    });
  }

  /**
   * 设置加载状态
   */
  setLoading(loading) {
    this.isLoading = loading;
    const loadingOverlay = document.getElementById('loading-overlay');
    
    if (loadingOverlay) {
      if (loading) {
        loadingOverlay.removeAttribute('hidden');
      } else {
        loadingOverlay.setAttribute('hidden', '');
      }
    }

    // 禁用/启用交互元素
    const interactiveElements = document.querySelectorAll('button, input, select');
    interactiveElements.forEach(element => {
      element.disabled = loading;
    });
  }

  /**
   * 显示错误消息
   */
  showError(message) {
    const errorToast = document.getElementById('error-toast');
    const errorMessage = document.getElementById('error-message');
    
    if (errorToast && errorMessage) {
      errorMessage.textContent = message;
      errorToast.removeAttribute('hidden');
      
      // 3秒后自动隐藏
      setTimeout(() => {
        errorToast.setAttribute('hidden', '');
      }, 3000);
    }
  }
}

// 当DOM加载完成后初始化
document.addEventListener('DOMContentLoaded', () => {
  new PopupController();
});
