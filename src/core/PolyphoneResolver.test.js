/**
 * 多音字智能识别器测试
 */

import { describe, it, expect, beforeEach } from 'vitest';
import { PolyphoneResolver } from './PolyphoneResolver.js';

describe('PolyphoneResolver', () => {
  let resolver;

  beforeEach(() => {
    resolver = new PolyphoneResolver();
  });

  describe('constructor', () => {
    it('should initialize with polyphone database', () => {
      expect(resolver.polyphoneDB).toBeInstanceOf(Map);
      expect(resolver.polyphoneDB.size).toBeGreaterThan(0);
    });

    it('should initialize caches', () => {
      expect(resolver.posRulesCache).toBeInstanceOf(Map);
      expect(resolver.grammarPatternsCache).toBeInstanceOf(Map);
    });
  });

  describe('exactWordMatch', () => {
    it('should match exact words in context', () => {
      const result1 = resolver.exactWordMatch('行', '我去银行');
      const result2 = resolver.exactWordMatch('行', '我要行走');
      
      expect(result1).toBe('háng');
      expect(result2).toBe('xíng');
    });

    it('should prioritize longer words', () => {
      // 如果同时包含"行"和"银行"，应该优先匹配"银行"
      const result = resolver.exactWordMatch('行', '银行业');
      expect(result).toBe('háng');
    });

    it('should return null for no match', () => {
      const result = resolver.exactWordMatch('行', '没有相关词汇');
      expect(result).toBeNull();
    });
  });

  describe('resolve', () => {
    it('should resolve polyphone characters correctly', () => {
      // 测试"行"字的不同读音
      const result1 = resolver.resolve('行', '我去银行取钱');
      const result2 = resolver.resolve('行', '我要行走回家');
      
      expect(result1).toBe('háng');
      expect(result2).toBe('xíng');
    });

    it('should resolve "中" character correctly', () => {
      const result1 = resolver.resolve('中', '中国是我的祖国');
      const result2 = resolver.resolve('中', '他中奖了');
      
      expect(result1).toBe('zhōng');
      expect(result2).toBe('zhòng');
    });

    it('should resolve "长" character correctly', () => {
      const result1 = resolver.resolve('长', '这条路很长');
      const result2 = resolver.resolve('长', '孩子长大了');
      
      expect(result1).toBe('cháng');
      expect(result2).toBe('zhǎng');
    });

    it('should resolve "重" character correctly', () => {
      const result1 = resolver.resolve('重', '这很重要');
      const result2 = resolver.resolve('重', '重复一遍');
      
      expect(result1).toBe('zhòng');
      expect(result2).toBe('chóng');
    });

    it('should return null for unknown characters', () => {
      const result = resolver.resolve('测', '这是测试');
      expect(result).toBeNull();
    });

    it('should handle empty context', () => {
      const result = resolver.resolve('行', '');
      expect(typeof result).toBe('string'); // 应该返回默认读音
    });
  });

  describe('grammarPatternMatch', () => {
    it('should match verb patterns', () => {
      const result = resolver.grammarPatternMatch('行', '正在行走');
      expect(result).toBe('xíng');
    });

    it('should match noun patterns', () => {
      const result = resolver.grammarPatternMatch('行', '银行业务');
      expect(result).toBe('háng');
    });

    it('should return null for no pattern match', () => {
      const result = resolver.grammarPatternMatch('行', '无关内容');
      expect(result).toBeNull();
    });
  });

  describe('semanticAnalysis', () => {
    it('should analyze semantic fields', () => {
      const result1 = resolver.semanticAnalysis('行', '银行金融');
      const result2 = resolver.semanticAnalysis('行', '走路步行');
      
      expect(result1).toBe('háng');
      expect(result2).toBe('xíng');
    });

    it('should return null for no semantic match', () => {
      const result = resolver.semanticAnalysis('行', '无关语义');
      expect(result).toBeNull();
    });
  });

  describe('frequencyAnalysis', () => {
    it('should return default pronunciations', () => {
      const result1 = resolver.frequencyAnalysis('行', '任意上下文');
      const result2 = resolver.frequencyAnalysis('中', '任意上下文');
      
      expect(result1).toBe('xíng');
      expect(result2).toBe('zhōng');
    });

    it('should return null for unknown characters', () => {
      const result = resolver.frequencyAnalysis('测', '任意上下文');
      expect(result).toBeNull();
    });
  });

  describe('batchResolve', () => {
    it('should resolve multiple polyphone characters', () => {
      const text = '我去银行，然后行走回家';
      const results = resolver.batchResolve(text);
      
      expect(Array.isArray(results)).toBe(true);
      expect(results.length).toBe(2); // 两个"行"字
      
      results.forEach(result => {
        expect(result).toHaveProperty('char');
        expect(result).toHaveProperty('index');
        expect(result).toHaveProperty('context');
        expect(result).toHaveProperty('pronunciation');
        expect(result).toHaveProperty('confidence');
      });
    });

    it('should return empty array for text without polyphone characters', () => {
      const text = '这是普通文本';
      const results = resolver.batchResolve(text);
      
      expect(Array.isArray(results)).toBe(true);
      expect(results.length).toBe(0);
    });
  });

  describe('calculateConfidence', () => {
    it('should return confidence between 0 and 1', () => {
      const confidence1 = resolver.calculateConfidence('行', '银行', 'háng');
      const confidence2 = resolver.calculateConfidence('行', '无关', 'xíng');
      
      expect(confidence1).toBeGreaterThanOrEqual(0);
      expect(confidence1).toBeLessThanOrEqual(1);
      expect(confidence2).toBeGreaterThanOrEqual(0);
      expect(confidence2).toBeLessThanOrEqual(1);
    });

    it('should return 0 for null pronunciation', () => {
      const confidence = resolver.calculateConfidence('行', '上下文', null);
      expect(confidence).toBe(0);
    });

    it('should give higher confidence for exact matches', () => {
      const exactConfidence = resolver.calculateConfidence('行', '银行', 'háng');
      const defaultConfidence = resolver.calculateConfidence('行', '无关', 'xíng');
      
      expect(exactConfidence).toBeGreaterThan(defaultConfidence);
    });
  });

  describe('addCustomRule', () => {
    it('should add new custom rule', () => {
      const customChar = '测';
      const customRule = { '测试': 'cè' };
      
      resolver.addCustomRule(customChar, customRule);
      
      const result = resolver.resolve(customChar, '这是测试');
      expect(result).toBe('cè');
    });

    it('should extend existing rules', () => {
      const existingChar = '行';
      const additionalRule = { '自行车': 'xíng' };
      
      resolver.addCustomRule(existingChar, additionalRule);
      
      const result = resolver.resolve(existingChar, '骑自行车');
      expect(result).toBe('xíng');
    });
  });

  describe('removeCustomRule', () => {
    it('should remove specific words from rules', () => {
      // 先添加自定义规则
      resolver.addCustomRule('测', { '测试': 'cè', '测量': 'cè' });
      
      // 移除其中一个
      resolver.removeCustomRule('测', ['测试']);
      
      // 验证移除结果
      const result1 = resolver.resolve('测', '这是测试');
      const result2 = resolver.resolve('测', '进行测量');
      
      expect(result1).toBeNull(); // 已移除
      expect(result2).toBe('cè');  // 仍存在
    });
  });

  describe('getSupportedPolyphoneChars', () => {
    it('should return array of supported characters', () => {
      const chars = resolver.getSupportedPolyphoneChars();
      
      expect(Array.isArray(chars)).toBe(true);
      expect(chars.length).toBeGreaterThan(0);
      expect(chars).toContain('行');
      expect(chars).toContain('中');
      expect(chars).toContain('长');
      expect(chars).toContain('重');
    });
  });

  describe('getStats', () => {
    it('should return statistics', () => {
      const stats = resolver.getStats();
      
      expect(stats).toHaveProperty('polyphoneCount');
      expect(stats).toHaveProperty('totalRules');
      expect(typeof stats.polyphoneCount).toBe('number');
      expect(typeof stats.totalRules).toBe('number');
      expect(stats.polyphoneCount).toBeGreaterThan(0);
      expect(stats.totalRules).toBeGreaterThan(0);
    });
  });

  describe('getSemanticFields', () => {
    it('should return semantic fields for known characters', () => {
      const fields = resolver.getSemanticFields('行');
      
      expect(Array.isArray(fields)).toBe(true);
      expect(fields.length).toBeGreaterThan(0);
      
      fields.forEach(field => {
        expect(Array.isArray(field)).toBe(true);
        expect(field.length).toBe(2);
        expect(Array.isArray(field[0])).toBe(true);
        expect(typeof field[1]).toBe('string');
      });
    });

    it('should return empty array for unknown characters', () => {
      const fields = resolver.getSemanticFields('测');
      expect(Array.isArray(fields)).toBe(true);
      expect(fields.length).toBe(0);
    });
  });

  describe('contextMatchesSemanticField', () => {
    it('should match semantic field keywords', () => {
      const semanticField = ['银', '金', '钱'];
      const result1 = resolver.contextMatchesSemanticField('去银行', semanticField);
      const result2 = resolver.contextMatchesSemanticField('买东西', semanticField);
      
      expect(result1).toBe(true);
      expect(result2).toBe(false);
    });
  });
});
