/**
 * 缓存管理器测试
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { CacheManager } from './CacheManager.js';

// Mock Chrome storage API
const mockChromeStorage = {
  local: {
    get: vi.fn(),
    set: vi.fn(),
    remove: vi.fn()
  }
};

// 设置全局chrome对象
global.chrome = {
  storage: mockChromeStorage,
  runtime: {}
};

describe('CacheManager', () => {
  let cacheManager;

  beforeEach(async () => {
    // 重置所有mock
    vi.clearAllMocks();
    mockChromeStorage.local.get.mockResolvedValue({});
    mockChromeStorage.local.set.mockResolvedValue();
    mockChromeStorage.local.remove.mockResolvedValue();

    cacheManager = new CacheManager({
      maxSize: 5,
      ttl: 1000,
      enablePersistence: false // 在测试中禁用持久化
    });

    // 等待初始化完成
    await new Promise(resolve => setTimeout(resolve, 10));
  });

  afterEach(() => {
    if (cacheManager) {
      cacheManager.destroy();
    }
  });

  describe('constructor', () => {
    it('should initialize with default options', () => {
      const cm = new CacheManager();
      expect(cm.maxSize).toBe(1000);
      expect(cm.ttl).toBe(30 * 60 * 1000);
      expect(cm.enablePersistence).toBe(true);
      cm.destroy();
    });

    it('should initialize with custom options', () => {
      const cm = new CacheManager({
        maxSize: 100,
        ttl: 5000,
        enablePersistence: false
      });

      expect(cm.maxSize).toBe(100);
      expect(cm.ttl).toBe(5000);
      expect(cm.enablePersistence).toBe(false);
      cm.destroy();
    });
  });

  describe('basic operations', () => {
    it('should set and get values', () => {
      cacheManager.set('key1', 'value1');
      expect(cacheManager.get('key1')).toBe('value1');
    });

    it('should return null for non-existent keys', () => {
      expect(cacheManager.get('nonexistent')).toBeNull();
    });

    it('should check if key exists', () => {
      cacheManager.set('key1', 'value1');
      expect(cacheManager.has('key1')).toBe(true);
      expect(cacheManager.has('nonexistent')).toBe(false);
    });

    it('should delete keys', () => {
      cacheManager.set('key1', 'value1');
      expect(cacheManager.has('key1')).toBe(true);

      const deleted = cacheManager.delete('key1');
      expect(deleted).toBe(true);
      expect(cacheManager.has('key1')).toBe(false);
    });

    it('should clear all cache', () => {
      cacheManager.set('key1', 'value1');
      cacheManager.set('key2', 'value2');

      cacheManager.clear();

      expect(cacheManager.has('key1')).toBe(false);
      expect(cacheManager.has('key2')).toBe(false);
    });
  });

  describe('TTL (Time To Live)', () => {
    it('should expire items after TTL', async () => {
      const shortTTLManager = new CacheManager({
        maxSize: 10,
        ttl: 50, // 50ms
        enablePersistence: false
      });

      shortTTLManager.set('key1', 'value1');
      expect(shortTTLManager.get('key1')).toBe('value1');

      // 等待过期
      await new Promise(resolve => setTimeout(resolve, 100));

      expect(shortTTLManager.get('key1')).toBeNull();
      shortTTLManager.destroy();
    });

    it('should support custom TTL for individual items', async () => {
      const shortTTLManager = new CacheManager({
        maxSize: 10,
        ttl: 1000,
        enablePersistence: false
      });

      shortTTLManager.set('key1', 'value1', 50); // 50ms TTL
      expect(shortTTLManager.get('key1')).toBe('value1');

      await new Promise(resolve => setTimeout(resolve, 100));

      expect(shortTTLManager.get('key1')).toBeNull();
      shortTTLManager.destroy();
    });
  });

  describe('LRU eviction', () => {
    it('should evict least recently used items when cache is full', () => {
      // 填满缓存
      for (let i = 0; i < 5; i++) {
        cacheManager.set(`key${i}`, `value${i}`);
      }

      // 访问key1，使其成为最近使用的
      cacheManager.get('key1');

      // 添加新项，应该淘汰key0（最久未使用）
      cacheManager.set('key5', 'value5');

      expect(cacheManager.has('key0')).toBe(false);
      expect(cacheManager.has('key1')).toBe(true);
      expect(cacheManager.has('key5')).toBe(true);
    });
  });

  describe('batch operations', () => {
    it('should support batch get', () => {
      cacheManager.set('key1', 'value1');
      cacheManager.set('key2', 'value2');
      cacheManager.set('key3', 'value3');

      const results = cacheManager.mget(['key1', 'key2', 'nonexistent']);

      expect(results.get('key1')).toBe('value1');
      expect(results.get('key2')).toBe('value2');
      expect(results.has('nonexistent')).toBe(false);
    });

    it('should support batch set with Map', () => {
      const items = new Map([
        ['key1', 'value1'],
        ['key2', 'value2']
      ]);

      cacheManager.mset(items);

      expect(cacheManager.get('key1')).toBe('value1');
      expect(cacheManager.get('key2')).toBe('value2');
    });

    it('should support batch set with Object', () => {
      const items = {
        key1: 'value1',
        key2: 'value2'
      };

      cacheManager.mset(items);

      expect(cacheManager.get('key1')).toBe('value1');
      expect(cacheManager.get('key2')).toBe('value2');
    });
  });

  describe('statistics', () => {
    it('should track cache hits and misses', () => {
      cacheManager.set('key1', 'value1');

      // Hit
      cacheManager.get('key1');

      // Miss
      cacheManager.get('nonexistent');

      const stats = cacheManager.getStats();
      expect(stats.hits).toBe(1);
      expect(stats.misses).toBe(1);
      expect(stats.hitRate).toBe(0.5);
    });

    it('should track cache size and evictions', () => {
      // 填满缓存
      for (let i = 0; i < 5; i++) {
        cacheManager.set(`key${i}`, `value${i}`);
      }

      let stats = cacheManager.getStats();
      expect(stats.size).toBe(5);
      expect(stats.evictions).toBe(0);

      // 触发淘汰
      cacheManager.set('key5', 'value5');

      stats = cacheManager.getStats();
      expect(stats.size).toBe(5);
      expect(stats.evictions).toBe(1);
    });
  });

  describe('persistence', () => {
    it('should attempt to load from persistence when enabled', async () => {
      const persistentManager = new CacheManager({
        maxSize: 10,
        ttl: 1000,
        enablePersistence: true
      });

      // 等待初始化完成
      await new Promise(resolve => setTimeout(resolve, 10));

      expect(mockChromeStorage.local.get).toHaveBeenCalledWith(['pinyinCache']);
      persistentManager.destroy();
    });

    it('should handle persistence load errors gracefully', async () => {
      mockChromeStorage.local.get.mockRejectedValue(new Error('Storage error'));

      const persistentManager = new CacheManager({
        maxSize: 10,
        ttl: 1000,
        enablePersistence: true
      });

      // 应该不会抛出错误
      await new Promise(resolve => setTimeout(resolve, 10));

      persistentManager.destroy();
    });
  });

  describe('cleanup', () => {
    it('should clean up expired items', async () => {
      const shortTTLManager = new CacheManager({
        maxSize: 10,
        ttl: 50,
        enablePersistence: false
      });

      shortTTLManager.set('key1', 'value1');
      shortTTLManager.set('key2', 'value2');

      // 等待过期
      await new Promise(resolve => setTimeout(resolve, 100));

      // 手动触发清理
      shortTTLManager.cleanupExpired();

      expect(shortTTLManager.has('key1')).toBe(false);
      expect(shortTTLManager.has('key2')).toBe(false);

      shortTTLManager.destroy();
    });
  });

  describe('size calculation', () => {
    it('should calculate size for different value types', () => {
      const stringSize = cacheManager.calculateSize('hello');
      const objectSize = cacheManager.calculateSize({ key: 'value' });
      const numberSize = cacheManager.calculateSize(42);

      expect(stringSize).toBe(10); // 5 chars * 2
      expect(objectSize).toBeGreaterThan(0);
      expect(numberSize).toBe(8);
    });
  });

  describe('destroy', () => {
    it('should clean up resources when destroyed', () => {
      cacheManager.set('key1', 'value1');

      cacheManager.destroy();

      expect(cacheManager.memoryCache.size).toBe(0);
      expect(cacheManager.accessOrder.size).toBe(0);
    });
  });
});
