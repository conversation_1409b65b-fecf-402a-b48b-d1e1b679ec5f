/**
 * Chrome拼音标注插件 - 多音字智能识别器
 * 负责根据上下文智能识别多音字的正确读音
 */

import { PolyphoneContext } from '../utils/constants.js';

export class PolyphoneResolver {
  constructor() {
    // 多音字数据库
    this.polyphoneDB = new Map(Object.entries(PolyphoneContext));
    
    // 词性规则缓存
    this.posRulesCache = new Map();
    
    // 语法模式缓存
    this.grammarPatternsCache = new Map();
    
    this.initGrammarPatterns();
  }

  /**
   * 初始化语法模式
   */
  initGrammarPatterns() {
    // 动词模式
    this.grammarPatternsCache.set('verb_patterns', {
      '行': {
        patterns: [/[在正要将].*行/, /行.*[走动进出]/],
        pronunciation: 'xíng'
      },
      '为': {
        patterns: [/为.*[做干]/],
        pronunciation: 'wéi'
      }
    });

    // 名词模式
    this.grammarPatternsCache.set('noun_patterns', {
      '行': {
        patterns: [/.*行[业界]/],
        pronunciation: 'háng'
      },
      '都': {
        patterns: [/[首古]都/, /都[市城]/],
        pronunciation: 'dū'
      }
    });

    // 助词模式
    this.grammarPatternsCache.set('particle_patterns', {
      '得': {
        patterns: [/.*得[很好快慢]/],
        pronunciation: 'de'
      }
    });
  }

  /**
   * 解析多音字的正确读音
   * @param {string} char - 多音字
   * @param {string} context - 上下文
   * @param {Object} options - 选项
   * @returns {string|null} 识别出的拼音，如果无法识别返回null
   */
  resolve(char, context, options = {}) {
    if (!this.polyphoneDB.has(char)) {
      return null; // 不是已知的多音字
    }

    // 1. 精确词汇匹配
    const exactMatch = this.exactWordMatch(char, context);
    if (exactMatch) {
      return exactMatch;
    }

    // 2. 语法模式匹配
    const grammarMatch = this.grammarPatternMatch(char, context);
    if (grammarMatch) {
      return grammarMatch;
    }

    // 3. 上下文语义分析
    const semanticMatch = this.semanticAnalysis(char, context);
    if (semanticMatch) {
      return semanticMatch;
    }

    // 4. 统计频率分析
    const frequencyMatch = this.frequencyAnalysis(char, context);
    if (frequencyMatch) {
      return frequencyMatch;
    }

    return null; // 无法确定
  }

  /**
   * 精确词汇匹配
   * @param {string} char - 多音字
   * @param {string} context - 上下文
   * @returns {string|null} 匹配的拼音
   */
  exactWordMatch(char, context) {
    const wordMap = this.polyphoneDB.get(char);
    if (!wordMap) return null;

    // 按词长度排序，优先匹配长词
    const sortedWords = Object.keys(wordMap).sort((a, b) => b.length - a.length);

    for (const word of sortedWords) {
      if (context.includes(word)) {
        return wordMap[word];
      }
    }

    return null;
  }

  /**
   * 语法模式匹配
   * @param {string} char - 多音字
   * @param {string} context - 上下文
   * @returns {string|null} 匹配的拼音
   */
  grammarPatternMatch(char, context) {
    const patternTypes = ['verb_patterns', 'noun_patterns', 'particle_patterns'];

    for (const patternType of patternTypes) {
      const patterns = this.grammarPatternsCache.get(patternType);
      if (patterns && patterns[char]) {
        const charPatterns = patterns[char];
        
        for (const pattern of charPatterns.patterns) {
          if (pattern.test(context)) {
            return charPatterns.pronunciation;
          }
        }
      }
    }

    return null;
  }

  /**
   * 上下文语义分析
   * @param {string} char - 多音字
   * @param {string} context - 上下文
   * @returns {string|null} 分析结果
   */
  semanticAnalysis(char, context) {
    // 基于语义场的分析
    const semanticFields = this.getSemanticFields(char);
    
    for (const [field, pronunciation] of semanticFields) {
      if (this.contextMatchesSemanticField(context, field)) {
        return pronunciation;
      }
    }

    return null;
  }

  /**
   * 获取字符的语义场
   * @param {string} char - 字符
   * @returns {Array} 语义场数组
   */
  getSemanticFields(char) {
    const semanticMap = {
      '行': [
        [['银', '金', '钱', '融', '贷', '存', '款'], 'háng'], // 金融领域
        [['走', '跑', '动', '移', '步', '路'], 'xíng']        // 行动领域
      ],
      '长': [
        [['大', '高', '成', '育', '老', '幼'], 'zhǎng'],     // 成长领域
        [['短', '宽', '窄', '深', '浅', '远'], 'cháng']      // 长度领域
      ],
      '重': [
        [['要', '大', '点', '心', '视', '点'], 'zhòng'],     // 重要性
        [['复', '新', '来', '叠', '复', '又'], 'chóng']      // 重复性
      ],
      '为': [
        [['了', '何', '什', '么', '啥', '谁'], 'wèi'],       // 目的
        [['人', '师', '父', '母', '官', '王'], 'wéi']        // 身份
      ]
    };

    return semanticMap[char] || [];
  }

  /**
   * 检查上下文是否匹配语义场
   * @param {string} context - 上下文
   * @param {Array} semanticField - 语义场关键词
   * @returns {boolean} 是否匹配
   */
  contextMatchesSemanticField(context, semanticField) {
    return semanticField.some(keyword => context.includes(keyword));
  }

  /**
   * 统计频率分析
   * @param {string} char - 多音字
   * @param {string} context - 上下文
   * @returns {string|null} 分析结果
   */
  frequencyAnalysis(char, context) {
    // 基于使用频率的默认读音
    const defaultPronunciations = {
      '行': 'xíng',  // 行走更常见
      '中': 'zhōng', // 中国、中心更常见
      '长': 'cháng', // 长度更常见
      '重': 'zhòng', // 重要更常见
      '还': 'hái',   // 还是、还有更常见
      '为': 'wèi',   // 为了更常见
      '得': 'de',    // 助词用法更常见
      '都': 'dōu'    // 都是更常见
    };

    return defaultPronunciations[char] || null;
  }

  /**
   * 批量解析文本中的多音字
   * @param {string} text - 文本
   * @returns {Array} 解析结果数组
   */
  batchResolve(text) {
    const results = [];
    const chars = Array.from(text);

    for (let i = 0; i < chars.length; i++) {
      const char = chars[i];
      
      if (this.polyphoneDB.has(char)) {
        // 获取上下文（前后各8个字符）
        const contextStart = Math.max(0, i - 8);
        const contextEnd = Math.min(chars.length, i + 9);
        const context = chars.slice(contextStart, contextEnd).join('');

        const pronunciation = this.resolve(char, context);
        
        results.push({
          char: char,
          index: i,
          context: context,
          pronunciation: pronunciation,
          confidence: this.calculateConfidence(char, context, pronunciation)
        });
      }
    }

    return results;
  }

  /**
   * 计算识别置信度
   * @param {string} char - 字符
   * @param {string} context - 上下文
   * @param {string} pronunciation - 识别的读音
   * @returns {number} 置信度 (0-1)
   */
  calculateConfidence(char, context, pronunciation) {
    if (!pronunciation) return 0;

    let confidence = 0.5; // 基础置信度

    // 精确词汇匹配的置信度最高
    if (this.exactWordMatch(char, context) === pronunciation) {
      confidence = 0.95;
    }
    // 语法模式匹配的置信度较高
    else if (this.grammarPatternMatch(char, context) === pronunciation) {
      confidence = 0.8;
    }
    // 语义分析的置信度中等
    else if (this.semanticAnalysis(char, context) === pronunciation) {
      confidence = 0.7;
    }
    // 频率分析的置信度较低
    else if (this.frequencyAnalysis(char, context) === pronunciation) {
      confidence = 0.6;
    }

    return confidence;
  }

  /**
   * 添加自定义多音字规则
   * @param {string} char - 多音字
   * @param {Object} wordMap - 词汇映射
   */
  addCustomRule(char, wordMap) {
    if (this.polyphoneDB.has(char)) {
      const existing = this.polyphoneDB.get(char);
      this.polyphoneDB.set(char, { ...existing, ...wordMap });
    } else {
      this.polyphoneDB.set(char, wordMap);
    }
  }

  /**
   * 移除自定义规则
   * @param {string} char - 多音字
   * @param {Array} words - 要移除的词汇
   */
  removeCustomRule(char, words) {
    if (!this.polyphoneDB.has(char)) return;

    const wordMap = this.polyphoneDB.get(char);
    words.forEach(word => delete wordMap[word]);
    
    if (Object.keys(wordMap).length === 0) {
      this.polyphoneDB.delete(char);
    }
  }

  /**
   * 获取支持的多音字列表
   * @returns {Array} 多音字列表
   */
  getSupportedPolyphoneChars() {
    return Array.from(this.polyphoneDB.keys());
  }

  /**
   * 获取统计信息
   * @returns {Object} 统计信息
   */
  getStats() {
    return {
      polyphoneCount: this.polyphoneDB.size,
      totalRules: Array.from(this.polyphoneDB.values())
        .reduce((sum, wordMap) => sum + Object.keys(wordMap).length, 0)
    };
  }
}

export default PolyphoneResolver;
