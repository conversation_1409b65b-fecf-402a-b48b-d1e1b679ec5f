/**
 * Chrome拼音标注插件 - 缓存管理器
 * 负责管理拼音标注的缓存，提升页面性能
 */

export class CacheManager {
  constructor(options = {}) {
    this.maxSize = options.maxSize || 1000;
    this.ttl = options.ttl || 30 * 60 * 1000; // 30分钟默认过期时间
    this.enablePersistence = options.enablePersistence !== false;

    // 内存缓存
    this.memoryCache = new Map();

    // LRU缓存实现
    this.accessOrder = new Map();

    // 缓存统计
    this.stats = {
      hits: 0,
      misses: 0,
      evictions: 0,
      persistenceLoads: 0,
      persistenceSaves: 0
    };

    // 初始化
    this.init();
  }

  /**
   * 初始化缓存管理器
   */
  async init() {
    if (this.enablePersistence) {
      try {
        await this.loadFromPersistence();
      } catch (error) {
        console.warn('Failed to initialize persistence:', error);
      }
    }

    // 定期清理过期缓存
    this.startCleanupTimer();
  }

  /**
   * 获取缓存项
   * @param {string} key - 缓存键
   * @returns {any|null} 缓存值，如果不存在或过期返回null
   */
  get(key) {
    const item = this.memoryCache.get(key);

    if (!item) {
      this.stats.misses++;
      return null;
    }

    // 检查是否过期
    if (this.isExpired(item)) {
      this.memoryCache.delete(key);
      this.accessOrder.delete(key);
      this.stats.misses++;
      return null;
    }

    // 更新访问时间和顺序
    item.lastAccessed = Date.now();
    this.updateAccessOrder(key);

    this.stats.hits++;
    return item.value;
  }

  /**
   * 设置缓存项
   * @param {string} key - 缓存键
   * @param {any} value - 缓存值
   * @param {number} customTTL - 自定义过期时间（可选）
   */
  set(key, value, customTTL = null) {
    const now = Date.now();
    const ttl = customTTL || this.ttl;

    const item = {
      value: value,
      createdAt: now,
      lastAccessed: now,
      expiresAt: now + ttl,
      size: this.calculateSize(value)
    };

    // 如果缓存已满，执行LRU淘汰
    if (this.memoryCache.size >= this.maxSize && !this.memoryCache.has(key)) {
      this.evictLRU();
    }

    this.memoryCache.set(key, item);
    this.updateAccessOrder(key);

    // 异步持久化
    if (this.enablePersistence) {
      this.saveToPersistenceAsync(key, item);
    }
  }

  /**
   * 删除缓存项
   * @param {string} key - 缓存键
   * @returns {boolean} 是否成功删除
   */
  delete(key) {
    const deleted = this.memoryCache.delete(key);
    this.accessOrder.delete(key);

    if (this.enablePersistence && deleted) {
      this.removeFromPersistenceAsync(key);
    }

    return deleted;
  }

  /**
   * 检查缓存项是否存在
   * @param {string} key - 缓存键
   * @returns {boolean} 是否存在
   */
  has(key) {
    const item = this.memoryCache.get(key);
    return Boolean(item && !this.isExpired(item));
  }

  /**
   * 清空所有缓存
   */
  clear() {
    this.memoryCache.clear();
    this.accessOrder.clear();

    if (this.enablePersistence) {
      this.clearPersistenceAsync();
    }
  }

  /**
   * 批量获取缓存项
   * @param {Array} keys - 缓存键数组
   * @returns {Map} 键值对映射
   */
  mget(keys) {
    const results = new Map();

    for (const key of keys) {
      const value = this.get(key);
      if (value !== null) {
        results.set(key, value);
      }
    }

    return results;
  }

  /**
   * 批量设置缓存项
   * @param {Map|Object} items - 键值对
   * @param {number} customTTL - 自定义过期时间（可选）
   */
  mset(items, customTTL = null) {
    const entries = items instanceof Map ? items.entries() : Object.entries(items);

    for (const [key, value] of entries) {
      this.set(key, value, customTTL);
    }
  }

  /**
   * 检查缓存项是否过期
   * @param {Object} item - 缓存项
   * @returns {boolean} 是否过期
   */
  isExpired(item) {
    return Date.now() > item.expiresAt;
  }

  /**
   * 更新访问顺序（LRU）
   * @param {string} key - 缓存键
   */
  updateAccessOrder(key) {
    // 删除旧的访问记录
    this.accessOrder.delete(key);
    // 添加到最后（最近访问）
    this.accessOrder.set(key, Date.now());
  }

  /**
   * 淘汰最久未使用的缓存项
   */
  evictLRU() {
    if (this.accessOrder.size === 0) return;

    // 获取最久未访问的键
    const oldestKey = this.accessOrder.keys().next().value;

    this.memoryCache.delete(oldestKey);
    this.accessOrder.delete(oldestKey);
    this.stats.evictions++;

    if (this.enablePersistence) {
      this.removeFromPersistenceAsync(oldestKey);
    }
  }

  /**
   * 计算值的大小（简化实现）
   * @param {any} value - 值
   * @returns {number} 大小估算
   */
  calculateSize(value) {
    if (typeof value === 'string') {
      return value.length * 2; // Unicode字符
    } else if (typeof value === 'object') {
      return JSON.stringify(value).length * 2;
    } else {
      return 8; // 基本类型
    }
  }

  /**
   * 启动清理定时器
   */
  startCleanupTimer() {
    // 每5分钟清理一次过期缓存
    this.cleanupTimer = setInterval(() => {
      this.cleanupExpired();
    }, 5 * 60 * 1000);
  }

  /**
   * 清理过期缓存
   */
  cleanupExpired() {
    const now = Date.now();
    const expiredKeys = [];

    for (const [key, item] of this.memoryCache.entries()) {
      if (now > item.expiresAt) {
        expiredKeys.push(key);
      }
    }

    for (const key of expiredKeys) {
      this.delete(key);
    }
  }

  /**
   * 从持久化存储加载缓存
   */
  async loadFromPersistence() {
    try {
      if (typeof chrome !== 'undefined' && chrome.storage) {
        const result = await chrome.storage.local.get(['pinyinCache']);
        if (result.pinyinCache) {
          const cacheData = JSON.parse(result.pinyinCache);

          for (const [key, item] of Object.entries(cacheData)) {
            if (!this.isExpired(item)) {
              this.memoryCache.set(key, item);
              this.updateAccessOrder(key);
            }
          }

          this.stats.persistenceLoads++;
        }
      }
    } catch (error) {
      console.warn('Failed to load cache from persistence:', error);
    }
  }

  /**
   * 异步保存到持久化存储
   * @param {string} key - 缓存键
   * @param {Object} item - 缓存项
   */
  async saveToPersistenceAsync(key, item) {
    // 使用防抖，避免频繁写入
    if (this.saveTimeout) {
      clearTimeout(this.saveTimeout);
    }

    this.saveTimeout = setTimeout(async () => {
      await this.saveToPersistence();
    }, 1000);
  }

  /**
   * 保存到持久化存储
   */
  async saveToPersistence() {
    try {
      if (typeof chrome !== 'undefined' && chrome.storage) {
        const cacheData = {};

        // 只保存未过期的缓存项
        for (const [key, item] of this.memoryCache.entries()) {
          if (!this.isExpired(item)) {
            cacheData[key] = item;
          }
        }

        await chrome.storage.local.set({
          pinyinCache: JSON.stringify(cacheData)
        });

        this.stats.persistenceSaves++;
      }
    } catch (error) {
      console.warn('Failed to save cache to persistence:', error);
    }
  }

  /**
   * 异步从持久化存储移除
   * @param {string} key - 缓存键
   */
  async removeFromPersistenceAsync(key) {
    // 简化实现：重新保存整个缓存
    await this.saveToPersistenceAsync(key, null);
  }

  /**
   * 异步清空持久化存储
   */
  async clearPersistenceAsync() {
    try {
      if (typeof chrome !== 'undefined' && chrome.storage) {
        await chrome.storage.local.remove(['pinyinCache']);
      }
    } catch (error) {
      console.warn('Failed to clear persistence cache:', error);
    }
  }

  /**
   * 获取缓存统计信息
   * @returns {Object} 统计信息
   */
  getStats() {
    const totalSize = Array.from(this.memoryCache.values())
      .reduce((sum, item) => sum + item.size, 0);

    return {
      ...this.stats,
      size: this.memoryCache.size,
      maxSize: this.maxSize,
      totalSize: totalSize,
      hitRate: this.stats.hits / (this.stats.hits + this.stats.misses) || 0
    };
  }

  /**
   * 销毁缓存管理器
   */
  destroy() {
    if (this.cleanupTimer) {
      clearInterval(this.cleanupTimer);
    }

    if (this.saveTimeout) {
      clearTimeout(this.saveTimeout);
    }

    this.clear();
  }
}

export default CacheManager;
