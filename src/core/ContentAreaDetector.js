/**
 * Chrome拼音标注插件 - 内容区域检测器
 * 负责智能识别页面主要内容区域，避免对导航、广告等区域进行标注
 */

export class ContentAreaDetector {
  constructor(options = {}) {
    this.minTextLength = options.minTextLength || 50;
    this.minChineseRatio = options.minChineseRatio || 0.1;
    this.excludeSelectors = options.excludeSelectors || this.getDefaultExcludeSelectors();
    this.contentSelectors = options.contentSelectors || this.getDefaultContentSelectors();
    
    // 缓存检测结果
    this.detectionCache = new WeakMap();
    
    // 统计信息
    this.stats = {
      totalElements: 0,
      contentElements: 0,
      excludedElements: 0,
      cacheHits: 0
    };
  }

  /**
   * 获取默认排除选择器
   * @returns {Array} 排除选择器数组
   */
  getDefaultExcludeSelectors() {
    return [
      // 导航相关
      'nav', 'navbar', '.nav', '.navbar', '.navigation', '.menu', '.breadcrumb',
      '[role="navigation"]', '[role="menubar"]', '[role="menu"]',
      
      // 页眉页脚
      'header', 'footer', '.header', '.footer', '.site-header', '.site-footer',
      
      // 侧边栏
      'aside', 'sidebar', '.sidebar', '.side-bar', '.aside',
      
      // 广告相关
      '.ad', '.ads', '.advertisement', '.banner', '.promo', '.promotion',
      '[class*="ad-"]', '[id*="ad-"]', '[class*="ads-"]', '[id*="ads-"]',
      '.google-ads', '.adsense', '.adsbygoogle',
      
      // 表单和控件
      'form', 'input', 'textarea', 'select', 'button', '.form', '.btn',
      
      // 脚本和样式
      'script', 'style', 'noscript', 'link', 'meta',
      
      // 隐藏元素
      '[style*="display: none"]', '[style*="display:none"]',
      '[style*="visibility: hidden"]', '[style*="visibility:hidden"]',
      '.hidden', '.hide', '.invisible',
      
      // 评论和社交
      '.comment', '.comments', '.social', '.share', '.sharing',
      
      // 其他非内容区域
      '.toolbar', '.statusbar', '.popup', '.modal', '.overlay',
      '.loading', '.spinner', '.placeholder'
    ];
  }

  /**
   * 获取默认内容选择器
   * @returns {Array} 内容选择器数组
   */
  getDefaultContentSelectors() {
    return [
      // 主要内容区域
      'main', 'article', 'section', '.main', '.content', '.article',
      '[role="main"]', '[role="article"]',
      
      // 常见内容容器
      '.post', '.entry', '.story', '.news', '.blog-post',
      '.content-area', '.main-content', '.primary-content',
      
      // 文本内容
      'p', 'div', 'span', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6',
      'li', 'td', 'th', 'blockquote', 'pre'
    ];
  }

  /**
   * 检测元素是否为内容区域
   * @param {Element} element - DOM元素
   * @returns {boolean} 是否为内容区域
   */
  isContentArea(element) {
    if (!element || element.nodeType !== Node.ELEMENT_NODE) {
      return false;
    }

    // 检查缓存
    if (this.detectionCache.has(element)) {
      this.stats.cacheHits++;
      return this.detectionCache.get(element);
    }

    this.stats.totalElements++;

    // 1. 检查是否被明确排除
    if (this.isExcludedElement(element)) {
      this.stats.excludedElements++;
      this.detectionCache.set(element, false);
      return false;
    }

    // 2. 检查是否为内容选择器匹配
    if (this.matchesContentSelectors(element)) {
      this.stats.contentElements++;
      this.detectionCache.set(element, true);
      return true;
    }

    // 3. 基于内容特征分析
    const isContent = this.analyzeContentFeatures(element);
    
    if (isContent) {
      this.stats.contentElements++;
    } else {
      this.stats.excludedElements++;
    }
    
    this.detectionCache.set(element, isContent);
    return isContent;
  }

  /**
   * 检查元素是否被排除
   * @param {Element} element - DOM元素
   * @returns {boolean} 是否被排除
   */
  isExcludedElement(element) {
    // 检查标签名
    const tagName = element.tagName.toLowerCase();
    const excludedTags = ['script', 'style', 'noscript', 'link', 'meta'];
    if (excludedTags.includes(tagName)) {
      return true;
    }

    // 检查选择器匹配
    for (const selector of this.excludeSelectors) {
      try {
        if (element.matches(selector)) {
          return true;
        }
      } catch (error) {
        // 忽略无效选择器
        continue;
      }
    }

    // 检查父元素是否被排除
    let parent = element.parentElement;
    while (parent) {
      for (const selector of this.excludeSelectors) {
        try {
          if (parent.matches(selector)) {
            return true;
          }
        } catch (error) {
          continue;
        }
      }
      parent = parent.parentElement;
    }

    return false;
  }

  /**
   * 检查是否匹配内容选择器
   * @param {Element} element - DOM元素
   * @returns {boolean} 是否匹配
   */
  matchesContentSelectors(element) {
    for (const selector of this.contentSelectors) {
      try {
        if (element.matches(selector)) {
          return true;
        }
      } catch (error) {
        continue;
      }
    }
    return false;
  }

  /**
   * 基于内容特征分析
   * @param {Element} element - DOM元素
   * @returns {boolean} 是否为内容区域
   */
  analyzeContentFeatures(element) {
    const text = this.getElementText(element);
    
    // 文本长度检查
    if (text.length < this.minTextLength) {
      return false;
    }

    // 中文字符比例检查
    const chineseRatio = this.calculateChineseRatio(text);
    if (chineseRatio < this.minChineseRatio) {
      return false;
    }

    // 元素位置和大小检查
    if (!this.isVisibleAndSized(element)) {
      return false;
    }

    // 内容密度检查
    if (!this.hasGoodContentDensity(element)) {
      return false;
    }

    return true;
  }

  /**
   * 获取元素文本内容
   * @param {Element} element - DOM元素
   * @returns {string} 文本内容
   */
  getElementText(element) {
    // 排除子元素中的排除区域
    let text = '';
    const walker = document.createTreeWalker(
      element,
      NodeFilter.SHOW_TEXT,
      {
        acceptNode: (node) => {
          const parent = node.parentElement;
          if (parent && this.isExcludedElement(parent)) {
            return NodeFilter.FILTER_REJECT;
          }
          return NodeFilter.FILTER_ACCEPT;
        }
      }
    );

    let node;
    while ((node = walker.nextNode())) {
      text += node.textContent;
    }

    return text.trim();
  }

  /**
   * 计算中文字符比例
   * @param {string} text - 文本
   * @returns {number} 中文字符比例 (0-1)
   */
  calculateChineseRatio(text) {
    if (!text) return 0;
    
    const chineseChars = text.match(/[\u4e00-\u9fff]/g);
    return chineseChars ? chineseChars.length / text.length : 0;
  }

  /**
   * 检查元素是否可见且有合适大小
   * @param {Element} element - DOM元素
   * @returns {boolean} 是否可见且有合适大小
   */
  isVisibleAndSized(element) {
    const rect = element.getBoundingClientRect();
    const style = window.getComputedStyle(element);
    
    // 检查是否隐藏
    if (style.display === 'none' || style.visibility === 'hidden') {
      return false;
    }

    // 检查大小
    if (rect.width < 10 || rect.height < 10) {
      return false;
    }

    return true;
  }

  /**
   * 检查内容密度
   * @param {Element} element - DOM元素
   * @returns {boolean} 是否有良好的内容密度
   */
  hasGoodContentDensity(element) {
    const text = element.textContent || '';
    const textLength = text.trim().length;
    
    if (textLength === 0) return false;

    // 计算子元素数量
    const childElements = element.querySelectorAll('*').length;
    
    // 内容密度 = 文本长度 / 子元素数量
    const density = childElements > 0 ? textLength / childElements : textLength;
    
    // 密度阈值（可调整）
    return density > 10;
  }

  /**
   * 获取页面主要内容区域
   * @param {Element} root - 根元素，默认为document.body
   * @returns {Array} 内容区域元素数组
   */
  getMainContentAreas(root = document.body) {
    const contentAreas = [];
    
    if (!root) return contentAreas;

    // 首先尝试找到明确的内容区域
    const explicitContentAreas = root.querySelectorAll(
      'main, article, [role="main"], [role="article"], .main, .content, .article'
    );

    for (const area of explicitContentAreas) {
      if (this.isContentArea(area)) {
        contentAreas.push(area);
      }
    }

    // 如果没有找到明确的内容区域，分析所有可能的区域
    if (contentAreas.length === 0) {
      const candidates = root.querySelectorAll('div, section, p');
      
      for (const candidate of candidates) {
        if (this.isContentArea(candidate)) {
          contentAreas.push(candidate);
        }
      }
    }

    return this.deduplicateAreas(contentAreas);
  }

  /**
   * 去重内容区域（移除嵌套的区域）
   * @param {Array} areas - 内容区域数组
   * @returns {Array} 去重后的数组
   */
  deduplicateAreas(areas) {
    return areas.filter(area => {
      // 检查是否被其他区域包含
      return !areas.some(otherArea => 
        otherArea !== area && otherArea.contains(area)
      );
    });
  }

  /**
   * 添加自定义排除选择器
   * @param {Array|string} selectors - 选择器
   */
  addExcludeSelectors(selectors) {
    const selectorArray = Array.isArray(selectors) ? selectors : [selectors];
    this.excludeSelectors.push(...selectorArray);
    this.clearCache();
  }

  /**
   * 添加自定义内容选择器
   * @param {Array|string} selectors - 选择器
   */
  addContentSelectors(selectors) {
    const selectorArray = Array.isArray(selectors) ? selectors : [selectors];
    this.contentSelectors.push(...selectorArray);
    this.clearCache();
  }

  /**
   * 清空检测缓存
   */
  clearCache() {
    this.detectionCache = new WeakMap();
    this.stats = {
      totalElements: 0,
      contentElements: 0,
      excludedElements: 0,
      cacheHits: 0
    };
  }

  /**
   * 获取统计信息
   * @returns {Object} 统计信息
   */
  getStats() {
    return { ...this.stats };
  }
}

export default ContentAreaDetector;
