/**
 * Chrome拼音标注插件 - 拼音转换器
 * 负责中文文本的拼音转换功能
 */

import { pinyin } from 'pinyin-pro';
import { PinyinFormats } from '../utils/constants.js';
import { RareCharDetector } from './RareCharDetector.js';
import { PolyphoneResolver } from './PolyphoneResolver.js';
import { CacheManager } from './CacheManager.js';

export class PinyinConverter {
  constructor(options = {}) {
    this.toneType = options.toneType || PinyinFormats.SYMBOL;
    this.polyphone = options.polyphone !== false; // 默认启用多音字处理
    this.rareWordOnly = options.rareWordOnly || false; // 是否只标注生僻字

    // 初始化高级缓存管理器
    this.cacheManager = new CacheManager({
      maxSize: options.cacheMaxSize || 2000,
      ttl: options.cacheTTL || 60 * 60 * 1000, // 1小时
      enablePersistence: options.enableCachePersistence !== false
    });

    // 保留简单缓存用于向后兼容
    this.cache = new Map();

    // 初始化生僻字检测器
    this.rareCharDetector = new RareCharDetector({
      frequencyThreshold: options.wordFrequencyThreshold || 5,
      customCommonChars: options.customCommonChars,
      customRareChars: options.customRareChars
    });

    // 初始化多音字识别器
    this.polyphoneResolver = new PolyphoneResolver();
  }

  /**
   * 单字转拼音
   * @param {string} char - 单个汉字
   * @param {string} context - 上下文文本，用于多音字识别
   * @returns {string} 拼音
   */
  convertChar(char, context = '') {
    // 检查高级缓存
    const cacheKey = `char_${char}_${context}_${this.toneType}_${this.polyphone}`;
    const cachedResult = this.cacheManager.get(cacheKey);
    if (cachedResult !== null) {
      return cachedResult;
    }

    let result;

    try {
      // 如果启用多音字处理且有上下文
      if (this.polyphone && context) {
        result = this.resolvePolyphone(char, context);
      }

      // 如果多音字处理没有结果，使用默认转换
      if (!result) {
        result = pinyin(char, {
          toneType: this.getToneTypeOption(),
          type: 'string'
        });
      }

      // 缓存结果
      this.cacheManager.set(cacheKey, result);

      // 同时更新简单缓存（向后兼容）
      const simpleCacheKey = `${char}_${context}_${this.toneType}`;
      this.cache.set(simpleCacheKey, result);

      return result;

    } catch (error) {
      console.warn('Failed to convert character:', char, error);
      return char; // 转换失败时返回原字符
    }
  }

  /**
   * 词组转拼音
   * @param {string} word - 中文词组
   * @returns {string} 拼音
   */
  convertWord(word) {
    if (!word || word.length === 0) return '';

    // 检查高级缓存
    const cacheKey = `word_${word}_${this.toneType}_${this.polyphone}`;
    const cachedResult = this.cacheManager.get(cacheKey);
    if (cachedResult !== null) {
      return cachedResult;
    }

    try {
      const result = pinyin(word, {
        toneType: this.getToneTypeOption(),
        type: 'string',
        separator: ' '
      });

      // 缓存结果
      this.cacheManager.set(cacheKey, result);

      // 同时更新简单缓存（向后兼容）
      const simpleCacheKey = `word_${word}_${this.toneType}`;
      this.cache.set(simpleCacheKey, result);

      return result;

    } catch (error) {
      console.warn('Failed to convert word:', word, error);
      return word; // 转换失败时返回原词组
    }
  }

  /**
   * 批量转换文本中的中文字符
   * @param {string} text - 包含中文的文本
   * @param {boolean} wordMode - 是否按词转换
   * @returns {Array} 转换结果数组，每个元素包含 {text, pinyin, isChinese, isRare}
   */
  convertText(text, wordMode = false) {
    if (!text) return [];

    if (wordMode) {
      // 词模式：需要先分词，这里先用简单的字符级处理
      // TODO: 集成分词功能
      return this.convertTextByChar(text);
    } else {
      return this.convertTextByChar(text);
    }
  }

  /**
   * 按字符转换文本
   * @param {string} text - 文本
   * @returns {Array} 转换结果
   */
  convertTextByChar(text) {
    const results = [];
    const chars = Array.from(text);

    for (let i = 0; i < chars.length; i++) {
      const char = chars[i];
      const isChinese = /[\u4e00-\u9fff]/.test(char);

      if (isChinese) {
        // 获取上下文（前后各3个字符）
        const contextStart = Math.max(0, i - 3);
        const contextEnd = Math.min(chars.length, i + 4);
        const context = chars.slice(contextStart, contextEnd).join('');

        // 检测是否为生僻字
        const isRare = this.rareCharDetector.isRareChar(char, context);

        // 如果启用了生僻字模式，只处理生僻字
        const shouldConvert = !this.rareWordOnly || isRare;

        const pinyin = shouldConvert ? this.convertChar(char, context) : '';

        results.push({
          text: char,
          pinyin: pinyin,
          isChinese: true,
          isRare: isRare,
          index: i
        });
      } else {
        results.push({
          text: char,
          pinyin: '',
          isChinese: false,
          isRare: false,
          index: i
        });
      }
    }

    return results;
  }

  /**
   * 多音字上下文识别
   * @param {string} char - 多音字
   * @param {string} context - 上下文
   * @returns {string|null} 识别出的拼音，如果无法识别返回null
   */
  resolvePolyphone(char, context) {
    return this.polyphoneResolver.resolve(char, context);
  }

  /**
   * 获取pinyin-pro库的声调类型选项
   * @returns {string} 声调类型
   */
  getToneTypeOption() {
    switch (this.toneType) {
      case PinyinFormats.SYMBOL:
        return 'symbol';
      case PinyinFormats.NUMBER:
        return 'num';
      case PinyinFormats.NONE:
        return 'none';
      default:
        return 'symbol';
    }
  }

  /**
   * 设置声调类型
   * @param {string} toneType - 声调类型
   */
  setToneType(toneType) {
    if (Object.values(PinyinFormats).includes(toneType)) {
      this.toneType = toneType;
      // 清空缓存，因为声调类型改变了
      this.cache.clear();
    }
  }

  /**
   * 启用/禁用多音字处理
   * @param {boolean} enabled - 是否启用
   */
  setPolyphoneEnabled(enabled) {
    this.polyphone = enabled;
    // 清空缓存
    this.cache.clear();
  }

  /**
   * 设置生僻字优先模式
   * @param {boolean} enabled - 是否只标注生僻字
   */
  setRareWordOnlyMode(enabled) {
    this.rareWordOnly = enabled;
    // 清空缓存，因为标注策略改变了
    this.cache.clear();
  }

  /**
   * 获取生僻字检测器
   * @returns {RareCharDetector} 生僻字检测器实例
   */
  getRareCharDetector() {
    return this.rareCharDetector;
  }

  /**
   * 检测文本中的生僻字
   * @param {string} text - 文本
   * @returns {Array} 生僻字信息数组
   */
  detectRareChars(text) {
    return this.rareCharDetector.detectRareChars(text);
  }

  /**
   * 获取多音字识别器
   * @returns {PolyphoneResolver} 多音字识别器实例
   */
  getPolyphoneResolver() {
    return this.polyphoneResolver;
  }

  /**
   * 批量识别文本中的多音字
   * @param {string} text - 文本
   * @returns {Array} 多音字识别结果
   */
  resolvePolyphoneInText(text) {
    return this.polyphoneResolver.batchResolve(text);
  }

  /**
   * 添加自定义多音字规则
   * @param {string} char - 多音字
   * @param {Object} wordMap - 词汇映射
   */
  addPolyphoneRule(char, wordMap) {
    this.polyphoneResolver.addCustomRule(char, wordMap);
    // 清空相关缓存
    this.clearCache();
  }

  /**
   * 获取缓存管理器
   * @returns {CacheManager} 缓存管理器实例
   */
  getCacheManager() {
    return this.cacheManager;
  }

  /**
   * 预热缓存 - 批量转换常用字符
   * @param {Array} chars - 字符数组
   * @param {Array} contexts - 上下文数组（可选）
   */
  async warmupCache(chars, contexts = []) {
    const promises = [];

    for (const char of chars) {
      if (contexts.length > 0) {
        for (const context of contexts) {
          promises.push(
            Promise.resolve().then(() => this.convertChar(char, context))
          );
        }
      } else {
        promises.push(
          Promise.resolve().then(() => this.convertChar(char))
        );
      }
    }

    await Promise.all(promises);
  }

  /**
   * 批量转换并缓存
   * @param {Array} items - 转换项数组 [{char, context}, ...]
   * @returns {Array} 转换结果数组
   */
  async batchConvertAndCache(items) {
    const results = [];
    const uncachedItems = [];

    // 首先检查缓存
    for (const item of items) {
      const cacheKey = `char_${item.char}_${item.context || ''}_${this.toneType}_${this.polyphone}`;
      const cachedResult = this.cacheManager.get(cacheKey);

      if (cachedResult !== null) {
        results.push({
          char: item.char,
          context: item.context,
          pinyin: cachedResult,
          fromCache: true
        });
      } else {
        uncachedItems.push(item);
      }
    }

    // 批量处理未缓存的项
    for (const item of uncachedItems) {
      const pinyin = this.convertChar(item.char, item.context || '');
      results.push({
        char: item.char,
        context: item.context,
        pinyin: pinyin,
        fromCache: false
      });
    }

    return results;
  }

  /**
   * 销毁转换器，清理资源
   */
  destroy() {
    this.cacheManager.destroy();
    this.cache.clear();
  }

  /**
   * 清空转换缓存
   */
  clearCache() {
    this.cache.clear();
    this.cacheManager.clear();
  }

  /**
   * 获取缓存统计信息
   * @returns {Object} 缓存统计
   */
  getCacheStats() {
    const advancedStats = this.cacheManager.getStats();

    return {
      // 简单缓存统计
      simpleCache: {
        size: this.cache.size,
        maxSize: 1000
      },
      // 高级缓存统计
      advancedCache: advancedStats,
      // 总体统计
      total: {
        size: this.cache.size + advancedStats.size,
        hitRate: advancedStats.hitRate
      }
    };
  }

  /**
   * 检查字符是否为中文
   * @param {string} char - 字符
   * @returns {boolean} 是否为中文
   */
  static isChinese(char) {
    return /[\u4e00-\u9fff]/.test(char);
  }

  /**
   * 检查文本是否包含中文
   * @param {string} text - 文本
   * @returns {boolean} 是否包含中文
   */
  static containsChinese(text) {
    return /[\u4e00-\u9fff]/.test(text);
  }
}

export default PinyinConverter;
