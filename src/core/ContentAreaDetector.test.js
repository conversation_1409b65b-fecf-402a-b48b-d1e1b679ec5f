/**
 * 内容区域检测器测试
 */

import { describe, it, expect, beforeEach, afterEach, vi } from 'vitest';
import { ContentAreaDetector } from './ContentAreaDetector.js';

// Mock DOM methods
const mockElement = (tagName, attributes = {}, textContent = '') => {
  const element = {
    tagName: tagName.toUpperCase(),
    textContent: textContent,
    matches: vi.fn(),
    getBoundingClientRect: vi.fn().mockReturnValue({
      width: 100,
      height: 100,
      top: 0,
      left: 0
    }),
    parentElement: null,
    contains: vi.fn(),
    querySelectorAll: vi.fn().mockReturnValue([]),
    ...attributes
  };
  
  // Mock matches method
  element.matches.mockImplementation((selector) => {
    if (selector === element.tagName.toLowerCase()) return true;
    if (attributes.className && selector.includes(attributes.className)) return true;
    if (attributes.id && selector.includes(attributes.id)) return true;
    return false;
  });
  
  return element;
};

// Mock window.getComputedStyle
global.window = {
  getComputedStyle: vi.fn().mockReturnValue({
    display: 'block',
    visibility: 'visible'
  })
};

// Mock document methods
global.document = {
  body: mockElement('body'),
  createTreeWalker: vi.fn().mockReturnValue({
    nextNode: vi.fn().mockReturnValue(null)
  })
};

// Mock NodeFilter
global.NodeFilter = {
  SHOW_TEXT: 4,
  FILTER_ACCEPT: 1,
  FILTER_REJECT: 2
};

// Mock Node
global.Node = {
  ELEMENT_NODE: 1,
  TEXT_NODE: 3
};

describe('ContentAreaDetector', () => {
  let detector;

  beforeEach(() => {
    detector = new ContentAreaDetector();
    vi.clearAllMocks();
  });

  afterEach(() => {
    vi.clearAllMocks();
  });

  describe('constructor', () => {
    it('should initialize with default options', () => {
      expect(detector.minTextLength).toBe(50);
      expect(detector.minChineseRatio).toBe(0.1);
      expect(Array.isArray(detector.excludeSelectors)).toBe(true);
      expect(Array.isArray(detector.contentSelectors)).toBe(true);
    });

    it('should initialize with custom options', () => {
      const customDetector = new ContentAreaDetector({
        minTextLength: 100,
        minChineseRatio: 0.2,
        excludeSelectors: ['.custom-exclude'],
        contentSelectors: ['.custom-content']
      });
      
      expect(customDetector.minTextLength).toBe(100);
      expect(customDetector.minChineseRatio).toBe(0.2);
      expect(customDetector.excludeSelectors).toContain('.custom-exclude');
      expect(customDetector.contentSelectors).toContain('.custom-content');
    });
  });

  describe('getDefaultExcludeSelectors', () => {
    it('should return array of exclude selectors', () => {
      const selectors = detector.getDefaultExcludeSelectors();
      
      expect(Array.isArray(selectors)).toBe(true);
      expect(selectors.length).toBeGreaterThan(0);
      expect(selectors).toContain('nav');
      expect(selectors).toContain('script');
      expect(selectors).toContain('.ad');
    });
  });

  describe('getDefaultContentSelectors', () => {
    it('should return array of content selectors', () => {
      const selectors = detector.getDefaultContentSelectors();
      
      expect(Array.isArray(selectors)).toBe(true);
      expect(selectors.length).toBeGreaterThan(0);
      expect(selectors).toContain('main');
      expect(selectors).toContain('article');
      expect(selectors).toContain('p');
    });
  });

  describe('isExcludedElement', () => {
    it('should exclude script elements', () => {
      const scriptElement = mockElement('script');
      expect(detector.isExcludedElement(scriptElement)).toBe(true);
    });

    it('should exclude style elements', () => {
      const styleElement = mockElement('style');
      expect(detector.isExcludedElement(styleElement)).toBe(true);
    });

    it('should exclude elements matching exclude selectors', () => {
      const navElement = mockElement('nav');
      expect(detector.isExcludedElement(navElement)).toBe(true);
    });

    it('should not exclude regular content elements', () => {
      const pElement = mockElement('p');
      expect(detector.isExcludedElement(pElement)).toBe(false);
    });
  });

  describe('matchesContentSelectors', () => {
    it('should match main elements', () => {
      const mainElement = mockElement('main');
      expect(detector.matchesContentSelectors(mainElement)).toBe(true);
    });

    it('should match article elements', () => {
      const articleElement = mockElement('article');
      expect(detector.matchesContentSelectors(articleElement)).toBe(true);
    });

    it('should match paragraph elements', () => {
      const pElement = mockElement('p');
      expect(detector.matchesContentSelectors(pElement)).toBe(true);
    });

    it('should not match non-content elements', () => {
      const scriptElement = mockElement('script');
      expect(detector.matchesContentSelectors(scriptElement)).toBe(false);
    });
  });

  describe('calculateChineseRatio', () => {
    it('should calculate Chinese character ratio correctly', () => {
      const text1 = '中文测试';
      const text2 = 'Hello 世界';
      const text3 = 'English only';
      
      expect(detector.calculateChineseRatio(text1)).toBe(1);
      expect(detector.calculateChineseRatio(text2)).toBeCloseTo(0.25);
      expect(detector.calculateChineseRatio(text3)).toBe(0);
    });

    it('should handle empty text', () => {
      expect(detector.calculateChineseRatio('')).toBe(0);
      expect(detector.calculateChineseRatio(null)).toBe(0);
    });
  });

  describe('isVisibleAndSized', () => {
    it('should return true for visible elements with good size', () => {
      const element = mockElement('div');
      element.getBoundingClientRect.mockReturnValue({
        width: 100,
        height: 100
      });
      
      expect(detector.isVisibleAndSized(element)).toBe(true);
    });

    it('should return false for hidden elements', () => {
      const element = mockElement('div');
      window.getComputedStyle.mockReturnValue({
        display: 'none',
        visibility: 'visible'
      });
      
      expect(detector.isVisibleAndSized(element)).toBe(false);
    });

    it('should return false for too small elements', () => {
      const element = mockElement('div');
      element.getBoundingClientRect.mockReturnValue({
        width: 5,
        height: 5
      });
      
      expect(detector.isVisibleAndSized(element)).toBe(false);
    });
  });

  describe('hasGoodContentDensity', () => {
    it('should return true for elements with good content density', () => {
      const element = mockElement('div');
      element.textContent = 'This is a long text content that should have good density';
      element.querySelectorAll.mockReturnValue([{}, {}]); // 2 child elements
      
      expect(detector.hasGoodContentDensity(element)).toBe(true);
    });

    it('should return false for elements with no text', () => {
      const element = mockElement('div');
      element.textContent = '';
      
      expect(detector.hasGoodContentDensity(element)).toBe(false);
    });

    it('should return false for elements with poor density', () => {
      const element = mockElement('div');
      element.textContent = 'Short';
      element.querySelectorAll.mockReturnValue(new Array(100).fill({})); // 100 child elements
      
      expect(detector.hasGoodContentDensity(element)).toBe(false);
    });
  });

  describe('isContentArea', () => {
    it('should return false for non-element nodes', () => {
      expect(detector.isContentArea(null)).toBe(false);
      expect(detector.isContentArea({ nodeType: Node.TEXT_NODE })).toBe(false);
    });

    it('should use cache for repeated checks', () => {
      const element = mockElement('p', {}, 'This is a test content with some Chinese 中文 text');
      
      // First call
      const result1 = detector.isContentArea(element);
      
      // Second call should use cache
      const result2 = detector.isContentArea(element);
      
      expect(result1).toBe(result2);
      expect(detector.stats.cacheHits).toBe(1);
    });

    it('should exclude script elements', () => {
      const scriptElement = mockElement('script');
      scriptElement.nodeType = Node.ELEMENT_NODE;
      
      expect(detector.isContentArea(scriptElement)).toBe(false);
    });

    it('should identify main elements as content areas', () => {
      const mainElement = mockElement('main');
      mainElement.nodeType = Node.ELEMENT_NODE;
      
      expect(detector.isContentArea(mainElement)).toBe(true);
    });
  });

  describe('addExcludeSelectors', () => {
    it('should add single selector', () => {
      const initialCount = detector.excludeSelectors.length;
      detector.addExcludeSelectors('.custom-exclude');
      
      expect(detector.excludeSelectors.length).toBe(initialCount + 1);
      expect(detector.excludeSelectors).toContain('.custom-exclude');
    });

    it('should add multiple selectors', () => {
      const initialCount = detector.excludeSelectors.length;
      detector.addExcludeSelectors(['.exclude1', '.exclude2']);
      
      expect(detector.excludeSelectors.length).toBe(initialCount + 2);
      expect(detector.excludeSelectors).toContain('.exclude1');
      expect(detector.excludeSelectors).toContain('.exclude2');
    });
  });

  describe('addContentSelectors', () => {
    it('should add single selector', () => {
      const initialCount = detector.contentSelectors.length;
      detector.addContentSelectors('.custom-content');
      
      expect(detector.contentSelectors.length).toBe(initialCount + 1);
      expect(detector.contentSelectors).toContain('.custom-content');
    });

    it('should add multiple selectors', () => {
      const initialCount = detector.contentSelectors.length;
      detector.addContentSelectors(['.content1', '.content2']);
      
      expect(detector.contentSelectors.length).toBe(initialCount + 2);
      expect(detector.contentSelectors).toContain('.content1');
      expect(detector.contentSelectors).toContain('.content2');
    });
  });

  describe('clearCache', () => {
    it('should clear detection cache and reset stats', () => {
      // Add some stats
      detector.stats.totalElements = 10;
      detector.stats.contentElements = 5;
      
      detector.clearCache();
      
      expect(detector.stats.totalElements).toBe(0);
      expect(detector.stats.contentElements).toBe(0);
      expect(detector.stats.excludedElements).toBe(0);
      expect(detector.stats.cacheHits).toBe(0);
    });
  });

  describe('getStats', () => {
    it('should return statistics object', () => {
      const stats = detector.getStats();
      
      expect(stats).toHaveProperty('totalElements');
      expect(stats).toHaveProperty('contentElements');
      expect(stats).toHaveProperty('excludedElements');
      expect(stats).toHaveProperty('cacheHits');
      
      expect(typeof stats.totalElements).toBe('number');
      expect(typeof stats.contentElements).toBe('number');
      expect(typeof stats.excludedElements).toBe('number');
      expect(typeof stats.cacheHits).toBe('number');
    });
  });
});
