/**
 * Chrome拼音标注插件 - 生僻字检测器
 * 负责识别生僻字并提供优先标注功能
 */

import { CommonCharacters } from '../utils/constants.js';

export class RareCharDetector {
  constructor(options = {}) {
    this.frequencyThreshold = options.frequencyThreshold || 5;
    this.customCommonChars = new Set(options.customCommonChars || []);
    this.customRareChars = new Set(options.customRareChars || []);

    // 合并常用字集合
    this.commonChars = new Set([
      ...CommonCharacters,
      ...this.customCommonChars
    ]);

    // 字频统计缓存
    this.frequencyCache = new Map();

    // 初始化扩展的常用字集合
    this.initExtendedCommonChars();
  }

  /**
   * 初始化扩展的常用字集合
   * 包含更多常见汉字
   */
  initExtendedCommonChars() {
    // 常用标点和符号
    const commonPunctuation = ['，', '。', '！', '？', '；', '：', '（', '）', '【', '】'];

    // 常用数字
    const commonNumbers = ['零', '一', '二', '三', '四', '五', '六', '七', '八', '九', '十', '百', '千', '万', '亿'];

    // 常用量词
    const commonMeasures = ['个', '只', '条', '张', '片', '本', '支', '根', '块', '颗', '粒', '滴', '份', '次'];

    // 常用方位词
    const commonDirections = ['上', '下', '左', '右', '前', '后', '东', '南', '西', '北', '中', '内', '外', '里'];

    // 常用时间词
    const commonTime = ['年', '月', '日', '时', '分', '秒', '今', '明', '昨', '早', '晚', '午', '夜'];

    // 常用代词
    const commonPronouns = ['我', '你', '他', '她', '它', '们', '这', '那', '什', '么', '谁', '哪', '怎'];

    // 合并所有扩展常用字
    const extendedCommon = [
      ...commonPunctuation,
      ...commonNumbers,
      ...commonMeasures,
      ...commonDirections,
      ...commonTime,
      ...commonPronouns
    ];

    extendedCommon.forEach(char => this.commonChars.add(char));
  }

  /**
   * 检测单个字符是否为生僻字
   * @param {string} char - 单个汉字
   * @param {string} context - 上下文，用于更准确的判断
   * @returns {boolean} 是否为生僻字
   */
  isRareChar(char, context = '') {
    // 非中文字符不是生僻字
    if (!/[\u4e00-\u9fff]/.test(char)) {
      return false;
    }

    // 检查自定义生僻字列表
    if (this.customRareChars.has(char)) {
      return true;
    }

    // 检查自定义常用字列表
    if (this.customCommonChars.has(char)) {
      return false;
    }

    // 检查基础常用字集合
    if (this.commonChars.has(char)) {
      return false;
    }

    // 基于上下文的判断
    if (context) {
      const contextRarity = this.analyzeContextRarity(char, context);
      if (contextRarity !== null) {
        return contextRarity;
      }
    }

    // 默认认为不在常用字集合中的字符为生僻字
    return true;
  }

  /**
   * 基于上下文分析字符的生僻程度
   * @param {string} char - 字符
   * @param {string} context - 上下文
   * @returns {boolean|null} 是否生僻，null表示无法判断
   */
  analyzeContextRarity(char, context) {
    // 检查是否在常见词汇中
    const commonWords = this.getCommonWordsContaining(char);

    for (const word of commonWords) {
      if (context.includes(word)) {
        return false; // 在常见词汇中，不是生僻字
      }
    }

    // 检查是否在专业术语中
    if (this.isInTechnicalTerm(char, context)) {
      return true; // 专业术语中的字符更可能是生僻字
    }

    return null; // 无法基于上下文判断
  }

  /**
   * 获取包含指定字符的常见词汇
   * @param {string} char - 字符
   * @returns {Array} 常见词汇列表
   */
  getCommonWordsContaining(char) {
    // 这里可以扩展为更完整的词汇数据库
    const commonWordMap = {
      '银': ['银行', '银色', '银子'],
      '钱': ['钱包', '钱币', '赚钱'],
      '铁': ['铁路', '铁门', '钢铁'],
      '金': ['金钱', '金色', '黄金'],
      '木': ['木头', '木材', '树木'],
      '水': ['水果', '水杯', '喝水'],
      '火': ['火车', '火灾', '点火'],
      '土': ['土地', '土壤', '泥土'],
      '石': ['石头', '石油', '岩石'],
      '草': ['草地', '草原', '青草'],
      '花': ['花朵', '花园', '鲜花'],
      '鸟': ['小鸟', '鸟类', '飞鸟'],
      '鱼': ['鱼类', '金鱼', '钓鱼'],
      '虫': ['昆虫', '虫子', '害虫']
    };

    return commonWordMap[char] || [];
  }

  /**
   * 检查是否在专业术语中
   * @param {string} char - 字符
   * @param {string} context - 上下文
   * @returns {boolean} 是否在专业术语中
   */
  isInTechnicalTerm(char, context) {
    // 医学术语特征
    const medicalIndicators = ['症', '病', '疗', '药', '医', '诊', '治'];

    // 法律术语特征
    const legalIndicators = ['法', '律', '条', '款', '规', '章', '令'];

    // 科技术语特征
    const techIndicators = ['技', '术', '科', '学', '研', '究', '发', '明'];

    const indicators = [...medicalIndicators, ...legalIndicators, ...techIndicators];

    return indicators.some(indicator => context.includes(indicator));
  }

  /**
   * 批量检测文本中的生僻字
   * @param {string} text - 文本
   * @returns {Array} 生僻字信息数组
   */
  detectRareChars(text) {
    const rareChars = [];
    const chars = Array.from(text);

    for (let i = 0; i < chars.length; i++) {
      const char = chars[i];

      if (/[\u4e00-\u9fff]/.test(char)) {
        // 获取上下文（前后各5个字符）
        const contextStart = Math.max(0, i - 5);
        const contextEnd = Math.min(chars.length, i + 6);
        const context = chars.slice(contextStart, contextEnd).join('');

        if (this.isRareChar(char, context)) {
          rareChars.push({
            char: char,
            index: i,
            context: context,
            rarity: this.calculateRarityScore(char, context)
          });
        }
      }
    }

    return rareChars;
  }

  /**
   * 计算字符的生僻程度分数
   * @param {string} char - 字符
   * @param {string} context - 上下文
   * @returns {number} 生僻程度分数 (0-10，10最生僻)
   */
  calculateRarityScore(char, context) {
    let score = 5; // 基础分数

    // 如果在自定义生僻字列表中，提高分数
    if (this.customRareChars.has(char)) {
      score += 3;
    }

    // 如果不在任何常用字集合中，提高分数
    if (!this.commonChars.has(char)) {
      score += 2;
    }

    // 基于上下文调整分数
    if (this.isInTechnicalTerm(char, context)) {
      score += 1;
    }

    // 确保分数在合理范围内
    return Math.min(10, Math.max(0, score));
  }

  /**
   * 设置频率阈值
   * @param {number} threshold - 新的阈值
   */
  setFrequencyThreshold(threshold) {
    this.frequencyThreshold = threshold;
  }

  /**
   * 添加自定义常用字
   * @param {Array|string} chars - 字符或字符数组
   */
  addCommonChars(chars) {
    const charArray = Array.isArray(chars) ? chars : [chars];
    charArray.forEach(char => {
      this.commonChars.add(char);
      this.customCommonChars.add(char);
    });
  }

  /**
   * 添加自定义生僻字
   * @param {Array|string} chars - 字符或字符数组
   */
  addRareChars(chars) {
    const charArray = Array.isArray(chars) ? chars : [chars];
    charArray.forEach(char => {
      this.customRareChars.add(char);
    });
  }

  /**
   * 移除自定义字符
   * @param {Array|string} chars - 字符或字符数组
   * @param {string} type - 'common' 或 'rare'
   */
  removeCustomChars(chars, type = 'common') {
    const charArray = Array.isArray(chars) ? chars : [chars];
    const targetSet = type === 'common' ? this.customCommonChars : this.customRareChars;

    charArray.forEach(char => {
      targetSet.delete(char);
      if (type === 'common') {
        this.commonChars.delete(char);
      }
    });
  }

  /**
   * 获取统计信息
   * @returns {Object} 统计信息
   */
  getStats() {
    return {
      commonCharsCount: this.commonChars.size,
      customCommonCharsCount: this.customCommonChars.size,
      customRareCharsCount: this.customRareChars.size,
      frequencyThreshold: this.frequencyThreshold
    };
  }

  /**
   * 重置为默认设置
   */
  reset() {
    this.customCommonChars.clear();
    this.customRareChars.clear();
    this.frequencyCache.clear();
    this.frequencyThreshold = 5; // 重置频率阈值
    this.commonChars = new Set(CommonCharacters);
    this.initExtendedCommonChars();
  }
}

export default RareCharDetector;
