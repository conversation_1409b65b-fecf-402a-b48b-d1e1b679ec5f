/**
 * 拼音转换器测试
 */

import { describe, it, expect, beforeEach } from 'vitest';
import { PinyinConverter } from './PinyinConverter.js';
import { PinyinFormats } from '../utils/constants.js';

describe('PinyinConverter', () => {
  let converter;

  beforeEach(() => {
    converter = new PinyinConverter();
  });

  describe('constructor', () => {
    it('should initialize with default options', () => {
      expect(converter.toneType).toBe(PinyinFormats.SYMBOL);
      expect(converter.polyphone).toBe(true);
      expect(converter.cache).toBeInstanceOf(Map);
    });

    it('should initialize with custom options', () => {
      const customConverter = new PinyinConverter({
        toneType: PinyinFormats.NUMBER,
        polyphone: false
      });

      expect(customConverter.toneType).toBe(PinyinFormats.NUMBER);
      expect(customConverter.polyphone).toBe(false);
    });
  });

  describe('convertChar', () => {
    it('should convert single Chinese character', () => {
      const result = converter.convertChar('中');
      expect(result).toBeTruthy();
      expect(typeof result).toBe('string');
    });

    it('should handle non-Chinese characters', () => {
      const result = converter.convertChar('a');
      expect(result).toBe('a');
    });

    it('should use cache for repeated conversions', () => {
      const char = '国';
      const result1 = converter.convertChar(char);
      const result2 = converter.convertChar(char);

      expect(result1).toBe(result2);
      expect(converter.cache.size).toBeGreaterThan(0);
    });

    it('should handle context for polyphone characters', () => {
      const result1 = converter.convertChar('中', '中国');
      const result2 = converter.convertChar('中', '中奖');

      expect(result1).toBeTruthy();
      expect(result2).toBeTruthy();
      // 结果可能不同，取决于上下文处理
    });
  });

  describe('convertWord', () => {
    it('should convert Chinese word', () => {
      const result = converter.convertWord('中国');
      expect(result).toBeTruthy();
      expect(typeof result).toBe('string');
      expect(result).toContain(' '); // 应该包含空格分隔符
    });

    it('should handle empty input', () => {
      const result = converter.convertWord('');
      expect(result).toBe('');
    });

    it('should cache word conversions', () => {
      const word = '测试';
      const result1 = converter.convertWord(word);
      const result2 = converter.convertWord(word);

      expect(result1).toBe(result2);
    });
  });

  describe('convertText', () => {
    it('should convert mixed text', () => {
      const text = 'Hello 世界';
      const results = converter.convertText(text);

      expect(Array.isArray(results)).toBe(true);
      expect(results.length).toBe(text.length);

      // 检查中文字符被正确标记
      const chineseResults = results.filter(r => r.isChinese);
      expect(chineseResults.length).toBe(2); // '世' 和 '界'

      chineseResults.forEach(result => {
        expect(result.pinyin).toBeTruthy();
        expect(typeof result.pinyin).toBe('string');
      });
    });

    it('should handle pure English text', () => {
      const text = 'Hello World';
      const results = converter.convertText(text);

      expect(results.every(r => !r.isChinese)).toBe(true);
      expect(results.every(r => r.pinyin === '')).toBe(true);
    });

    it('should handle pure Chinese text', () => {
      const text = '你好世界';
      const results = converter.convertText(text);

      expect(results.every(r => r.isChinese)).toBe(true);
      expect(results.every(r => r.pinyin.length > 0)).toBe(true);
    });
  });

  describe('setToneType', () => {
    it('should update tone type', () => {
      converter.setToneType(PinyinFormats.NUMBER);
      expect(converter.toneType).toBe(PinyinFormats.NUMBER);
    });

    it('should clear cache when tone type changes', () => {
      converter.convertChar('中'); // 添加到缓存

      converter.setToneType(PinyinFormats.NUMBER);
      expect(converter.cache.size).toBe(0);
    });

    it('should ignore invalid tone types', () => {
      const originalToneType = converter.toneType;
      converter.setToneType('invalid');
      expect(converter.toneType).toBe(originalToneType);
    });
  });

  describe('setPolyphoneEnabled', () => {
    it('should update polyphone setting', () => {
      converter.setPolyphoneEnabled(false);
      expect(converter.polyphone).toBe(false);
    });

    it('should clear cache when polyphone setting changes', () => {
      converter.convertChar('中'); // 添加到缓存

      converter.setPolyphoneEnabled(false);
      expect(converter.cache.size).toBe(0);
    });
  });

  describe('clearCache', () => {
    it('should clear all cached results', () => {
      converter.convertChar('中');
      converter.convertWord('测试');

      expect(converter.cache.size).toBeGreaterThan(0);

      converter.clearCache();
      expect(converter.cache.size).toBe(0);
    });
  });

  describe('getCacheStats', () => {
    it('should return cache statistics', () => {
      const stats = converter.getCacheStats();

      expect(stats).toHaveProperty('simpleCache');
      expect(stats).toHaveProperty('advancedCache');
      expect(stats).toHaveProperty('total');

      expect(stats.simpleCache).toHaveProperty('size');
      expect(stats.simpleCache).toHaveProperty('maxSize');
      expect(typeof stats.simpleCache.size).toBe('number');
      expect(typeof stats.simpleCache.maxSize).toBe('number');
    });
  });

  describe('static methods', () => {
    describe('isChinese', () => {
      it('should identify Chinese characters', () => {
        expect(PinyinConverter.isChinese('中')).toBe(true);
        expect(PinyinConverter.isChinese('a')).toBe(false);
        expect(PinyinConverter.isChinese('1')).toBe(false);
        expect(PinyinConverter.isChinese('！')).toBe(false);
      });
    });

    describe('containsChinese', () => {
      it('should detect Chinese in text', () => {
        expect(PinyinConverter.containsChinese('Hello 世界')).toBe(true);
        expect(PinyinConverter.containsChinese('Hello World')).toBe(false);
        expect(PinyinConverter.containsChinese('中国')).toBe(true);
        expect(PinyinConverter.containsChinese('')).toBe(false);
      });
    });
  });

  describe('error handling', () => {
    it('should handle conversion errors gracefully', () => {
      // 模拟转换错误的情况
      const result = converter.convertChar('中');
      expect(result).toBeTruthy(); // 应该返回有效结果或原字符
    });
  });
});
