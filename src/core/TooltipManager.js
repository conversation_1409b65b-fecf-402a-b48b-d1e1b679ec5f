/**
 * Chrome拼音标注插件 - 悬浮提示管理器
 * 负责管理悬浮拼音提示的显示和隐藏
 */

// import { CSSClasses } from '../utils/constants.js'; // 暂时不使用

export class TooltipManager {
  constructor(pinyinConverter) {
    this.pinyinConverter = pinyinConverter;
    this.currentTooltip = null;
    this.tooltipContainer = null;
    this.isEnabled = false;
    this.settings = {};

    // 绑定事件处理函数
    this.handleMouseEnter = this.handleMouseEnter.bind(this);
    this.handleMouseLeave = this.handleMouseLeave.bind(this);
    this.handleMouseMove = this.handleMouseMove.bind(this);

    this.init();
  }

  /**
   * 初始化悬浮提示管理器
   */
  init() {
    this.createTooltipContainer();
    this.injectStyles();
  }

  /**
   * 创建悬浮提示容器
   */
  createTooltipContainer() {
    if (this.tooltipContainer) return;

    this.tooltipContainer = document.createElement('div');
    this.tooltipContainer.id = 'pinyin-tooltip-container';
    this.tooltipContainer.className = 'pinyin-tooltip-container';

    // 设置基础样式
    Object.assign(this.tooltipContainer.style, {
      position: 'absolute',
      zIndex: '999999',
      pointerEvents: 'none',
      visibility: 'hidden',
      opacity: '0',
      transition: 'opacity 0.2s ease, visibility 0.2s ease'
    });

    document.body.appendChild(this.tooltipContainer);
  }

  /**
   * 注入悬浮提示样式
   */
  injectStyles() {
    if (document.getElementById('pinyin-tooltip-styles')) return;

    const styleElement = document.createElement('style');
    styleElement.id = 'pinyin-tooltip-styles';
    styleElement.textContent = this.getTooltipCSS();
    document.head.appendChild(styleElement);
  }

  /**
   * 获取悬浮提示CSS样式
   */
  getTooltipCSS() {
    return `
      .pinyin-tooltip-container {
        font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif !important;
        font-size: 14px !important;
        line-height: 1.4 !important;
        color: #333 !important;
        background: rgba(0, 0, 0, 0.9) !important;
        border: 1px solid rgba(255, 255, 255, 0.2) !important;
        border-radius: 6px !important;
        padding: 8px 12px !important;
        box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3) !important;
        backdrop-filter: blur(10px) !important;
        max-width: 300px !important;
        word-wrap: break-word !important;
      }

      .pinyin-tooltip-content {
        color: white !important;
        margin: 0 !important;
        padding: 0 !important;
      }

      .pinyin-tooltip-char {
        font-size: 18px !important;
        font-weight: bold !important;
        color: #4CAF50 !important;
        margin-bottom: 4px !important;
      }

      .pinyin-tooltip-pinyin {
        font-size: 16px !important;
        color: #2196F3 !important;
        margin-bottom: 2px !important;
      }

      .pinyin-tooltip-info {
        font-size: 12px !important;
        color: #ccc !important;
        opacity: 0.8 !important;
      }

      .pinyin-hover-highlight {
        background-color: rgba(33, 150, 243, 0.1) !important;
        border-radius: 2px !important;
        cursor: help !important;
        transition: background-color 0.2s ease !important;
      }

      .pinyin-hover-highlight:hover {
        background-color: rgba(33, 150, 243, 0.2) !important;
      }
    `;
  }

  /**
   * 启用悬浮提示功能
   * @param {Object} settings - 设置选项
   */
  enable(settings = {}) {
    this.isEnabled = true;
    this.settings = settings;
    this.attachEventListeners();
  }

  /**
   * 禁用悬浮提示功能
   */
  disable() {
    this.isEnabled = false;
    this.detachEventListeners();
    this.hideTooltip();
    this.removeHighlights();
  }

  /**
   * 为中文字符添加悬浮事件监听
   */
  attachEventListeners() {
    // 使用事件委托，监听整个文档的鼠标事件
    document.addEventListener('mouseover', this.handleMouseEnter, true);
    document.addEventListener('mouseout', this.handleMouseLeave, true);
    document.addEventListener('mousemove', this.handleMouseMove, true);
  }

  /**
   * 移除事件监听
   */
  detachEventListeners() {
    document.removeEventListener('mouseover', this.handleMouseEnter, true);
    document.removeEventListener('mouseout', this.handleMouseLeave, true);
    document.removeEventListener('mousemove', this.handleMouseMove, true);
  }

  /**
   * 处理鼠标进入事件
   * @param {MouseEvent} event - 鼠标事件
   */
  handleMouseEnter(event) {
    if (!this.isEnabled) return;

    const target = event.target;
    const textContent = target.textContent;

    // 检查是否包含中文字符
    if (!textContent || !/[\u4e00-\u9fff]/.test(textContent)) {
      return;
    }

    // 检查是否是文本节点或包含文本的元素
    if (target.nodeType === Node.TEXT_NODE ||
      (target.nodeType === Node.ELEMENT_NODE && this.isValidTextElement(target))) {

      // 获取鼠标位置对应的字符
      const charInfo = this.getCharacterAtPosition(event, target);
      if (charInfo && charInfo.char && /[\u4e00-\u9fff]/.test(charInfo.char)) {
        this.showTooltipForCharacter(charInfo.char, textContent, event);
        this.highlightCharacter(target, charInfo);
      }
    }
  }

  /**
   * 处理鼠标离开事件
   * @param {MouseEvent} event - 鼠标事件
   */
  handleMouseLeave(event) {
    if (!this.isEnabled) return;

    // 延迟隐藏，避免鼠标快速移动时闪烁
    setTimeout(() => {
      if (!this.isMouseOverTooltip(event)) {
        this.hideTooltip();
        this.removeHighlights();
      }
    }, 100);
  }

  /**
   * 处理鼠标移动事件
   * @param {MouseEvent} event - 鼠标事件
   */
  handleMouseMove(event) {
    if (!this.isEnabled || !this.currentTooltip) return;

    // 更新提示框位置
    this.updateTooltipPosition(event);
  }

  /**
   * 检查元素是否为有效的文本元素
   * @param {Element} element - DOM元素
   * @returns {boolean} 是否有效
   */
  isValidTextElement(element) {
    const tagName = element.tagName.toLowerCase();
    const excludedTags = ['script', 'style', 'noscript', 'iframe', 'canvas', 'svg'];

    return !excludedTags.includes(tagName) &&
      !element.classList.contains('pinyin-tooltip-container') &&
      !element.closest('.pinyin-tooltip-container');
  }

  /**
   * 获取鼠标位置对应的字符信息
   * @param {MouseEvent} event - 鼠标事件
   * @param {Element} target - 目标元素
   * @returns {Object|null} 字符信息
   */
  getCharacterAtPosition(event, target) {
    // 简化实现：获取元素的第一个中文字符
    // 实际应用中可以使用更精确的位置计算
    const text = target.textContent;
    const chineseMatch = text.match(/[\u4e00-\u9fff]/);

    if (chineseMatch) {
      return {
        char: chineseMatch[0],
        index: chineseMatch.index,
        element: target
      };
    }

    return null;
  }

  /**
   * 显示字符的悬浮提示
   * @param {string} char - 中文字符
   * @param {string} context - 上下文
   * @param {MouseEvent} event - 鼠标事件
   */
  showTooltipForCharacter(char, context, event) {
    const pinyin = this.pinyinConverter.convertChar(char, context);

    const tooltipContent = this.createTooltipContent(char, pinyin);
    this.tooltipContainer.innerHTML = tooltipContent;

    this.updateTooltipPosition(event);
    this.showTooltip();

    this.currentTooltip = { char, pinyin, context };
  }

  /**
   * 创建提示框内容
   * @param {string} char - 中文字符
   * @param {string} pinyin - 拼音
   * @returns {string} HTML内容
   */
  createTooltipContent(char, pinyin) {
    return `
      <div class="pinyin-tooltip-content">
        <div class="pinyin-tooltip-char">${char}</div>
        <div class="pinyin-tooltip-pinyin">${pinyin}</div>
        <div class="pinyin-tooltip-info">拼音标注</div>
      </div>
    `;
  }

  /**
   * 更新提示框位置
   * @param {MouseEvent} event - 鼠标事件
   */
  updateTooltipPosition(event) {
    if (!this.tooltipContainer) return;

    const tooltip = this.tooltipContainer;
    const offset = 10;

    let x = event.pageX + offset;
    let y = event.pageY - tooltip.offsetHeight - offset;

    // 防止提示框超出视窗
    const viewportWidth = window.innerWidth;
    const scrollX = window.pageXOffset;
    const scrollY = window.pageYOffset;

    if (x + tooltip.offsetWidth > scrollX + viewportWidth) {
      x = event.pageX - tooltip.offsetWidth - offset;
    }

    if (y < scrollY) {
      y = event.pageY + offset;
    }

    tooltip.style.left = x + 'px';
    tooltip.style.top = y + 'px';
  }

  /**
   * 显示提示框
   */
  showTooltip() {
    if (this.tooltipContainer) {
      this.tooltipContainer.style.visibility = 'visible';
      this.tooltipContainer.style.opacity = '1';
    }
  }

  /**
   * 隐藏提示框
   */
  hideTooltip() {
    if (this.tooltipContainer) {
      this.tooltipContainer.style.visibility = 'hidden';
      this.tooltipContainer.style.opacity = '0';
    }
    this.currentTooltip = null;
  }

  /**
   * 高亮字符
   * @param {Element} element - 目标元素
   * @param {Object} _charInfo - 字符信息（暂未使用）
   */
  highlightCharacter(element, _charInfo) {
    element.classList.add('pinyin-hover-highlight');
  }

  /**
   * 移除高亮
   */
  removeHighlights() {
    const highlighted = document.querySelectorAll('.pinyin-hover-highlight');
    highlighted.forEach(el => el.classList.remove('pinyin-hover-highlight'));
  }

  /**
   * 检查鼠标是否在提示框上
   * @param {MouseEvent} event - 鼠标事件
   * @returns {boolean} 是否在提示框上
   */
  isMouseOverTooltip(event) {
    return this.tooltipContainer &&
      this.tooltipContainer.contains(event.relatedTarget);
  }

  /**
   * 销毁提示管理器
   */
  destroy() {
    this.disable();

    if (this.tooltipContainer) {
      this.tooltipContainer.remove();
      this.tooltipContainer = null;
    }

    const styleElement = document.getElementById('pinyin-tooltip-styles');
    if (styleElement) {
      styleElement.remove();
    }
  }
}

export default TooltipManager;
