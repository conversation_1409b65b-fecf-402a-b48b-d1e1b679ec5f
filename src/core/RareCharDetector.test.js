/**
 * 生僻字检测器测试
 */

import { describe, it, expect, beforeEach } from 'vitest';
import { RareCharDetector } from './RareCharDetector.js';

describe('RareCharDetector', () => {
  let detector;

  beforeEach(() => {
    detector = new RareCharDetector();
  });

  describe('constructor', () => {
    it('should initialize with default options', () => {
      expect(detector.frequencyThreshold).toBe(5);
      expect(detector.customCommonChars).toBeInstanceOf(Set);
      expect(detector.customRareChars).toBeInstanceOf(Set);
      expect(detector.commonChars).toBeInstanceOf(Set);
    });

    it('should initialize with custom options', () => {
      const customDetector = new RareCharDetector({
        frequencyThreshold: 10,
        customCommonChars: ['测', '试'],
        customRareChars: ['罕', '见']
      });
      
      expect(customDetector.frequencyThreshold).toBe(10);
      expect(customDetector.customCommonChars.has('测')).toBe(true);
      expect(customDetector.customRareChars.has('罕')).toBe(true);
    });
  });

  describe('isRareChar', () => {
    it('should identify common characters as not rare', () => {
      // 测试一些常用字
      expect(detector.isRareChar('的')).toBe(false);
      expect(detector.isRareChar('一')).toBe(false);
      expect(detector.isRareChar('是')).toBe(false);
      expect(detector.isRareChar('在')).toBe(false);
    });

    it('should identify non-Chinese characters as not rare', () => {
      expect(detector.isRareChar('a')).toBe(false);
      expect(detector.isRareChar('1')).toBe(false);
      expect(detector.isRareChar('!')).toBe(false);
    });

    it('should identify uncommon characters as rare', () => {
      // 测试一些不常用的字
      expect(detector.isRareChar('囧')).toBe(true);
      expect(detector.isRareChar('槑')).toBe(true);
      expect(detector.isRareChar('嘤')).toBe(true);
    });

    it('should respect custom common characters', () => {
      detector.addCommonChars(['囧']);
      expect(detector.isRareChar('囧')).toBe(false);
    });

    it('should respect custom rare characters', () => {
      detector.addRareChars(['的']);
      expect(detector.isRareChar('的')).toBe(true);
    });

    it('should consider context in detection', () => {
      // 在不同上下文中的同一个字可能有不同的判断
      const char = '银';
      const commonContext = '银行';
      const rareContext = '银杏';
      
      const result1 = detector.isRareChar(char, commonContext);
      const result2 = detector.isRareChar(char, rareContext);
      
      // 至少应该有一致的结果
      expect(typeof result1).toBe('boolean');
      expect(typeof result2).toBe('boolean');
    });
  });

  describe('detectRareChars', () => {
    it('should detect rare characters in text', () => {
      const text = '这是一个测试囧槑的文本';
      const rareChars = detector.detectRareChars(text);
      
      expect(Array.isArray(rareChars)).toBe(true);
      
      // 应该检测到一些生僻字
      const rareCharTexts = rareChars.map(r => r.char);
      expect(rareCharTexts.some(char => ['囧', '槑'].includes(char))).toBe(true);
    });

    it('should return empty array for text without rare characters', () => {
      const text = '这是一个普通的测试文本';
      const rareChars = detector.detectRareChars(text);
      
      expect(Array.isArray(rareChars)).toBe(true);
      // 可能为空或包含很少的生僻字
    });

    it('should include context and position information', () => {
      const text = '测试囧字';
      const rareChars = detector.detectRareChars(text);
      
      if (rareChars.length > 0) {
        const rareChar = rareChars[0];
        expect(rareChar).toHaveProperty('char');
        expect(rareChar).toHaveProperty('index');
        expect(rareChar).toHaveProperty('context');
        expect(rareChar).toHaveProperty('rarity');
        
        expect(typeof rareChar.index).toBe('number');
        expect(typeof rareChar.context).toBe('string');
        expect(typeof rareChar.rarity).toBe('number');
      }
    });
  });

  describe('calculateRarityScore', () => {
    it('should return a score between 0 and 10', () => {
      const score1 = detector.calculateRarityScore('的', '这是的');
      const score2 = detector.calculateRarityScore('囧', '很囧的');
      
      expect(score1).toBeGreaterThanOrEqual(0);
      expect(score1).toBeLessThanOrEqual(10);
      expect(score2).toBeGreaterThanOrEqual(0);
      expect(score2).toBeLessThanOrEqual(10);
    });

    it('should give higher scores to rarer characters', () => {
      const commonScore = detector.calculateRarityScore('的', '这是的');
      const rareScore = detector.calculateRarityScore('囧', '很囧的');
      
      // 生僻字应该有更高的分数
      expect(rareScore).toBeGreaterThan(commonScore);
    });
  });

  describe('getCommonWordsContaining', () => {
    it('should return array of common words', () => {
      const words = detector.getCommonWordsContaining('银');
      expect(Array.isArray(words)).toBe(true);
    });

    it('should return empty array for characters not in map', () => {
      const words = detector.getCommonWordsContaining('囧');
      expect(Array.isArray(words)).toBe(true);
      expect(words.length).toBe(0);
    });
  });

  describe('addCommonChars', () => {
    it('should add single character to common chars', () => {
      const char = '囧';
      detector.addCommonChars(char);
      
      expect(detector.customCommonChars.has(char)).toBe(true);
      expect(detector.commonChars.has(char)).toBe(true);
      expect(detector.isRareChar(char)).toBe(false);
    });

    it('should add multiple characters to common chars', () => {
      const chars = ['囧', '槑'];
      detector.addCommonChars(chars);
      
      chars.forEach(char => {
        expect(detector.customCommonChars.has(char)).toBe(true);
        expect(detector.commonChars.has(char)).toBe(true);
        expect(detector.isRareChar(char)).toBe(false);
      });
    });
  });

  describe('addRareChars', () => {
    it('should add single character to rare chars', () => {
      const char = '的';
      detector.addRareChars(char);
      
      expect(detector.customRareChars.has(char)).toBe(true);
      expect(detector.isRareChar(char)).toBe(true);
    });

    it('should add multiple characters to rare chars', () => {
      const chars = ['的', '是'];
      detector.addRareChars(chars);
      
      chars.forEach(char => {
        expect(detector.customRareChars.has(char)).toBe(true);
        expect(detector.isRareChar(char)).toBe(true);
      });
    });
  });

  describe('removeCustomChars', () => {
    it('should remove custom common characters', () => {
      const char = '囧';
      detector.addCommonChars(char);
      expect(detector.isRareChar(char)).toBe(false);
      
      detector.removeCustomChars(char, 'common');
      expect(detector.customCommonChars.has(char)).toBe(false);
    });

    it('should remove custom rare characters', () => {
      const char = '的';
      detector.addRareChars(char);
      expect(detector.isRareChar(char)).toBe(true);
      
      detector.removeCustomChars(char, 'rare');
      expect(detector.customRareChars.has(char)).toBe(false);
    });
  });

  describe('setFrequencyThreshold', () => {
    it('should update frequency threshold', () => {
      detector.setFrequencyThreshold(10);
      expect(detector.frequencyThreshold).toBe(10);
    });
  });

  describe('getStats', () => {
    it('should return statistics object', () => {
      const stats = detector.getStats();
      
      expect(stats).toHaveProperty('commonCharsCount');
      expect(stats).toHaveProperty('customCommonCharsCount');
      expect(stats).toHaveProperty('customRareCharsCount');
      expect(stats).toHaveProperty('frequencyThreshold');
      
      expect(typeof stats.commonCharsCount).toBe('number');
      expect(typeof stats.customCommonCharsCount).toBe('number');
      expect(typeof stats.customRareCharsCount).toBe('number');
      expect(typeof stats.frequencyThreshold).toBe('number');
    });
  });

  describe('reset', () => {
    it('should reset to default state', () => {
      // 添加一些自定义设置
      detector.addCommonChars(['囧']);
      detector.addRareChars(['的']);
      detector.setFrequencyThreshold(10);
      
      // 重置
      detector.reset();
      
      // 验证重置结果
      expect(detector.customCommonChars.size).toBe(0);
      expect(detector.customRareChars.size).toBe(0);
      expect(detector.frequencyThreshold).toBe(5);
    });
  });

  describe('isInTechnicalTerm', () => {
    it('should detect technical terms', () => {
      const result1 = detector.isInTechnicalTerm('症', '这是一种病症');
      const result2 = detector.isInTechnicalTerm('法', '这是法律条文');
      const result3 = detector.isInTechnicalTerm('术', '这是技术文档');
      
      expect(typeof result1).toBe('boolean');
      expect(typeof result2).toBe('boolean');
      expect(typeof result3).toBe('boolean');
    });
  });
});
