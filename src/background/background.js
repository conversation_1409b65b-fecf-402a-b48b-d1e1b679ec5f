/**
 * Chrome拼音标注插件 - Service Worker (Background Script)
 * 负责处理插件的后台逻辑、消息路由和状态管理
 */

import { MessageTypes } from '../utils/constants.js';
import { StorageManager } from '../utils/storage.js';
import { Logger } from '../utils/logger.js';

class BackgroundService {
  constructor() {
    this.storageManager = new StorageManager();
    this.logger = new Logger('Background');
    this.activeTabStates = new Map(); // 存储各标签页的状态

    this.init();
  }

  /**
   * 初始化后台服务
   */
  async init() {
    try {
      // 监听扩展安装/更新事件
      chrome.runtime.onInstalled.addListener(this.handleInstalled.bind(this));

      // 监听消息
      chrome.runtime.onMessage.addListener(this.handleMessage.bind(this));

      // 监听标签页更新
      chrome.tabs.onUpdated.addListener(this.handleTabUpdated.bind(this));

      // 监听标签页激活
      chrome.tabs.onActivated.addListener(this.handleTabActivated.bind(this));

      // 监听标签页关闭
      chrome.tabs.onRemoved.addListener(this.handleTabRemoved.bind(this));

      // 初始化默认设置
      await this.initializeSettings();

      this.logger.info('Background service initialized successfully');
    } catch (error) {
      this.logger.error('Failed to initialize background service', error);
    }
  }

  /**
   * 处理扩展安装/更新事件
   */
  async handleInstalled(details) {
    try {
      if (details.reason === 'install') {
        this.logger.info('Extension installed');
        // 首次安装时的初始化逻辑
        await this.onFirstInstall();
      } else if (details.reason === 'update') {
        this.logger.info('Extension updated', { previousVersion: details.previousVersion });
        // 更新时的迁移逻辑
        await this.onUpdate(details.previousVersion);
      }
    } catch (error) {
      this.logger.error('Error handling installation', error);
    }
  }

  /**
   * 首次安装初始化
   */
  async onFirstInstall() {
    // 设置默认配置
    const defaultSettings = {
      enabled: false,
      mode: 'word',
      displayStyle: 'ruby',
      toneType: 'symbol',
      scope: 'all',
      rareWordOnly: false,
      autoEnable: false,
      excludeDomains: [],
      fontSize: 'medium',
      colorTheme: 'blue',
      wordFrequencyThreshold: 5
    };

    await this.storageManager.saveSettings(defaultSettings);

    // 初始化统计数据
    await this.storageManager.initStats();

    this.logger.info('First install initialization completed');
  }

  /**
   * 更新时的处理
   */
  async onUpdate(previousVersion) {
    // 这里可以添加版本迁移逻辑
    this.logger.info(`Updated from version ${previousVersion}`);
  }

  /**
   * 初始化默认设置
   */
  async initializeSettings() {
    const settings = await this.storageManager.getSettings();
    if (!settings) {
      await this.onFirstInstall();
    }
  }

  /**
   * 处理消息
   */
  async handleMessage(message, sender) {
    try {
      this.logger.debug('Received message', { type: message.type, sender: sender.tab?.id });

      switch (message.type) {
        case MessageTypes.GET_STATUS:
          return await this.handleGetStatus(sender.tab?.id);

        case MessageTypes.TOGGLE_EXTENSION:
          return await this.handleToggleExtension(sender.tab?.id);

        case MessageTypes.CHANGE_MODE:
          return await this.handleChangeMode(message.data, sender.tab?.id);

        case MessageTypes.UPDATE_SETTINGS:
          return await this.handleUpdateSettings(message.data);

        case MessageTypes.STATUS_UPDATE:
          return await this.handleStatusUpdate(message.data, sender.tab?.id);

        case MessageTypes.ERROR_REPORT:
          return await this.handleErrorReport(message.data, sender.tab?.id);

        default:
          this.logger.warn('Unknown message type', { type: message.type });
          return { success: false, error: 'Unknown message type' };
      }
    } catch (error) {
      this.logger.error('Error handling message', error);
      return { success: false, error: error.message };
    }
  }

  /**
   * 获取当前状态
   */
  async handleGetStatus(tabId) {
    const settings = await this.storageManager.getSettings();
    const tabState = this.activeTabStates.get(tabId) || { enabled: false };

    return {
      success: true,
      data: {
        ...settings,
        tabEnabled: tabState.enabled,
        tabId
      }
    };
  }

  /**
   * 切换扩展启用状态
   */
  async handleToggleExtension(tabId) {
    const tabState = this.activeTabStates.get(tabId) || { enabled: false };
    const newEnabled = !tabState.enabled;

    // 更新标签页状态
    this.activeTabStates.set(tabId, { ...tabState, enabled: newEnabled });

    // 发送消息到content script
    const messageType = newEnabled ? MessageTypes.ENABLE_PINYIN : MessageTypes.DISABLE_PINYIN;
    const settings = await this.storageManager.getSettings();

    try {
      await chrome.tabs.sendMessage(tabId, {
        type: messageType,
        data: settings
      });

      // 更新图标状态
      await this.updateBadge(tabId, newEnabled);

      // 更新统计数据
      if (newEnabled) {
        await this.storageManager.incrementUsage();
      }

      this.logger.info(`Extension ${newEnabled ? 'enabled' : 'disabled'} for tab ${tabId}`);

      return {
        success: true,
        data: { enabled: newEnabled }
      };
    } catch (error) {
      this.logger.error('Failed to toggle extension', error);
      return {
        success: false,
        error: 'Failed to communicate with content script'
      };
    }
  }

  /**
   * 更改标注模式
   */
  async handleChangeMode(data, tabId) {
    const settings = await this.storageManager.getSettings();
    settings.mode = data.mode;

    await this.storageManager.saveSettings(settings);

    // 如果当前标签页已启用，发送更新消息
    const tabState = this.activeTabStates.get(tabId);
    if (tabState?.enabled) {
      try {
        await chrome.tabs.sendMessage(tabId, {
          type: MessageTypes.UPDATE_SETTINGS,
          data: settings
        });
      } catch (error) {
        this.logger.warn('Failed to update content script settings', error);
      }
    }

    return { success: true, data: settings };
  }

  /**
   * 更新设置
   */
  async handleUpdateSettings(data) {
    await this.storageManager.saveSettings(data);

    // 通知所有活跃的标签页更新设置
    for (const [tabId, tabState] of this.activeTabStates.entries()) {
      if (tabState.enabled) {
        try {
          await chrome.tabs.sendMessage(tabId, {
            type: MessageTypes.UPDATE_SETTINGS,
            data
          });
        } catch (error) {
          this.logger.warn(`Failed to update settings for tab ${tabId}`, error);
        }
      }
    }

    return { success: true };
  }

  /**
   * 处理状态更新
   */
  async handleStatusUpdate(data, tabId) {
    const tabState = this.activeTabStates.get(tabId) || {};
    this.activeTabStates.set(tabId, { ...tabState, ...data });

    return { success: true };
  }

  /**
   * 处理错误报告
   */
  async handleErrorReport(data, tabId) {
    this.logger.error('Error reported from content script', {
      tabId,
      error: data
    });

    // 这里可以添加错误统计和上报逻辑

    return { success: true };
  }

  /**
   * 处理标签页更新
   */
  async handleTabUpdated(tabId, changeInfo, tab) {
    // 当页面完成加载时，检查是否需要自动启用
    if (changeInfo.status === 'complete' && tab.url) {
      const settings = await this.storageManager.getSettings();

      if (settings.autoEnable && !this.isExcludedDomain(tab.url, settings.excludeDomains)) {
        // 自动启用拼音标注
        this.activeTabStates.set(tabId, { enabled: true });

        try {
          await chrome.tabs.sendMessage(tabId, {
            type: MessageTypes.ENABLE_PINYIN,
            data: settings
          });

          await this.updateBadge(tabId, true);
        } catch (error) {
          this.logger.warn(`Failed to auto-enable for tab ${tabId}`, error);
        }
      }
    }
  }

  /**
   * 处理标签页激活
   */
  async handleTabActivated(activeInfo) {
    const tabState = this.activeTabStates.get(activeInfo.tabId);
    if (tabState) {
      await this.updateBadge(activeInfo.tabId, tabState.enabled);
    }
  }

  /**
   * 处理标签页关闭
   */
  handleTabRemoved(tabId) {
    this.activeTabStates.delete(tabId);
    this.logger.debug(`Cleaned up state for closed tab ${tabId}`);
  }

  /**
   * 更新扩展图标徽章
   */
  async updateBadge(tabId, enabled) {
    try {
      await chrome.action.setBadgeText({
        text: enabled ? '开' : '',
        tabId
      });

      await chrome.action.setBadgeBackgroundColor({
        color: enabled ? '#4CAF50' : '#9E9E9E',
        tabId
      });
    } catch (error) {
      this.logger.warn('Failed to update badge', error);
    }
  }

  /**
   * 检查域名是否在排除列表中
   */
  isExcludedDomain(url, excludeDomains) {
    try {
      const hostname = new URL(url).hostname;
      return excludeDomains.some(domain =>
        hostname === domain || hostname.endsWith('.' + domain)
      );
    } catch {
      return false;
    }
  }
}

// 初始化后台服务
new BackgroundService();
